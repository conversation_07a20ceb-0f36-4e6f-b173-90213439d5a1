import { Image } from "lucide-react";

export function GalleryHeader({
  imagesCount,
  sharedCount,
}: {
  imagesCount: number;
  sharedCount: number;
}) {
  return (
    <div className="mb-2 page-transition">
      <div className="flex items-center gap-3">
        {/* Gallery Icon */}
        <div className="w-12 h-12 flex items-center justify-center">
          <Image className="h-8 w-8 text-blue-400" />
        </div>
        
        {/* Title and Count */}
        <div className="flex flex-col">
          <h1 className="text-4xl font-bold gradient-text leading-tight">
            My Gallery
          </h1>
          <p className="text-gray-400 text-sm">
            {imagesCount} images • {sharedCount} shared
          </p>
        </div>
      </div>
    </div>
  );
}
