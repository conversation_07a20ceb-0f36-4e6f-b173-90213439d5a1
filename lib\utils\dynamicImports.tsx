import React from 'react';
import { Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';

// Directly export motion components with proper typing
export const Motion = {
  div: motion.div,
  h1: motion.h1,
  p: motion.p,
  footer: motion.footer,
  span: motion.span,
} as const;

// Loading component for Suspense fallback
export const LoadingFallback = () => {
  return (
    <div className="flex items-center justify-center p-4">
      <Loader2 className="h-8 w-8 animate-spin text-violet-500" />
    </div>
  );
};
