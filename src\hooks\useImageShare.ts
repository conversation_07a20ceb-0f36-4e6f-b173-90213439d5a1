
import { useCallback, useState } from "react";
import { toast } from "sonner";
import { errorHandler } from "@/lib/utils/errorHandler";

/**
 * useImageShare - Hook for copying a link to clipboard and showing feedback.
 * The native Web Share API functionality has been removed in favor of a custom popover.
 * @returns {Object} { copyToClipboard, isCopying }
 */
export function useImageShare() {
  const [isCopying, setIsCopying] = useState(false);

  const copyToClipboard = useCallback(async (url: string) => {
    if (isCopying) return;
    setIsCopying(true);
    try {
      await navigator.clipboard.writeText(url);
      toast.success("Link copied to clipboard!");
    } catch (err) {
      errorHandler(err, { userMessage: "Failed to copy link." });
    } finally {
      setIsCopying(false);
    }
  }, [isCopying]);

  return { copyToClipboard, isCopying };
}
