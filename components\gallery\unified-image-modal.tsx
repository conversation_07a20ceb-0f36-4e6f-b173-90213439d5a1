"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import dynamic from "next/dynamic";
import { Download, Share2, Send, Trash2, Calendar, Sparkles, Heart, Loader2 } from "lucide-react";
import { GeneratedImage } from "@/lib/types";
import { cn } from "@/lib/utils";
import { createBrowserClient } from '@supabase/ssr';
import { ImageUrlHandler } from "@/lib/image-url-handler";
import useSWR from "swr";
import { useStyles } from "@/lib/contexts/StylesContext";
import { useUser } from "@/lib/contexts/UserContext";
import { useImageLikeStatus } from "@/lib/hooks/useImageLikeStatus";
import { useLikeToggle } from "@/lib/hooks/useLikeToggle";
import { useImageDownload } from "@/lib/hooks/useImageDownload";
import { useImageShare } from "@/lib/hooks/useImageShare";
import { useImageDelete } from "@/lib/hooks/useImageDelete";
import { formatStyleName, formatDateLocale } from "@/lib/utils/format";
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent, TooltipArrow } from "@/components/ui/tooltip";
import { SocialSharePopover } from "./social-share-popover";

interface UnifiedImageModalProps {
  image: GeneratedImage;
  isOpen: boolean;
  onClose: () => void;
  onShare?: (imageId: string, isShared: boolean) => void;
  onDelete?: (imageId: string) => void;
  showActions?: boolean;
}

export function UnifiedImageModal({
  image,
  isOpen,
  onClose,
  onShare,
  onDelete,
  showActions = true,
}: UnifiedImageModalProps) {
  const supabase = React.useMemo(() => createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  ), []);
  const [imageLoaded, setImageLoaded] = useState(false);
  const { styles } = useStyles();
  const { user } = useUser();
  const [optimisticIsShared, setOptimisticIsShared] = useState(image.is_shared);
  useEffect(() => { setOptimisticIsShared(image.is_shared); }, [image.is_shared]);

  const { likeCount, userLiked, isLoadingLikeStatus, mutateLikeStatus } = useImageLikeStatus(image.id, image.like_count);
  const isOwnImage = Boolean(user && user.id === image.user_id);
  const { handleLikeToggle, isLiking } = useLikeToggle({
    imageId: image.id,
    likeCount: likeCount ?? 0,
    userLiked,
    mutateLikeStatus,
    isOwnImage,
    user,
    isLoadingLikeStatus,
  });

  const { downloadImage, isDownloading } = useImageDownload();
  const { share: shareImage } = useImageShare();
  const { deleteImage, isDeleting } = useImageDelete();
  const styleDisplayName = formatStyleName(image.style);
  
  const DynamicDeleteConfirmationModal = dynamic(() => import("@/components/ui/delete-confirmation-modal").then(mod => mod.DeleteConfirmationModal), { ssr: false, loading: () => null });
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const fetchCachedUrl = React.useCallback(async () => {
    return await ImageUrlHandler.getCachedImageUrl(image.id, image.image_url);
  }, [image.id, image.image_url]);

  const { data: displayUrl, error: swrError, isLoading: swrLoading, mutate } = useSWR(
    [image.id, image.image_url, "cached"],
    fetchCachedUrl,
    { revalidateOnFocus: false, isPaused: () => !isOpen }
  );
  
  const imageError = !!swrError;
  const isLoading = swrLoading;

  useEffect(() => {
    if (!isOpen) {
      setImageLoaded(false);
      mutate();
    }
  }, [isOpen, mutate]);

  const handleDownload = React.useCallback(async () => {
    await downloadImage({
      displayUrl: displayUrl!,
      styleName: styleDisplayName,
      createdAt: image.created_at,
      imageId: image.id,
    });
  }, [displayUrl, styleDisplayName, image.created_at, image.id, downloadImage]);

  const handleShare = async () => {
    const imageUrl = `${window.location.origin}/images/${image.id}`;
    await shareImage(imageUrl, "Check out this AI artwork!");
  };

  const handleCommunityShare = React.useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation();
    setOptimisticIsShared(prev => !prev);
    onShare?.(image.id, !image.is_shared);
  }, [onShare, image.id, image.is_shared]);

  const handleDeleteConfirm = React.useCallback(async () => {
    const result = await deleteImage(image.id, image);
    if (result.success) {
      setIsDeleteModalOpen(false);
      onClose(); // Close modal on successful delete
      onDelete?.(image.id);
    }
    // Error toast is handled inside the hook
  }, [deleteImage, image, onClose, onDelete]);

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-5xl w-full max-h-[98vh] p-0 bg-transparent border-none shadow-none rounded-none">
          <DialogTitle className="sr-only">{styleDisplayName} AI Generated Artwork</DialogTitle>
          <DialogDescription className="sr-only">AI generated artwork in {styleDisplayName} style.</DialogDescription>
          <div className="relative mx-auto" style={{ maxWidth: 'min(90vw, 900px)', maxHeight: 'min(90vh, 900px)' }}>
            <div className="absolute top-3 left-3 z-30 flex flex-col sm:flex-row gap-2 sm:gap-2 items-start sm:items-center">
              <Badge className="px-2 py-0.5 bg-slate-900/80 backdrop-blur-sm text-white text-[11px] font-medium rounded-md border border-slate-700/50">
                <span className="bg-gradient-to-r from-violet-400 to-blue-400 bg-clip-text text-transparent font-semibold">{styleDisplayName}</span>
              </Badge>
              {optimisticIsShared && (
                <Badge className="px-2 py-0.5 bg-emerald-900/80 backdrop-blur-md text-emerald-200 text-[11px] font-medium rounded-md border border-emerald-700/50 flex items-center gap-1">
                  <Share2 className="h-3 w-3" />
                  <span>Shared</span>
                </Badge>
              )}
              <Badge className="px-2 py-0.5 bg-slate-800/80 backdrop-blur-md text-slate-400 text-[11px] font-medium rounded-md border border-slate-700/50 flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{image.created_at ? formatDateLocale(image.created_at) : "-"}</span>
              </Badge>
            </div>
            <button onClick={onClose} aria-label="Close modal" className="absolute top-3 right-3 z-30 h-8 w-8 flex items-center justify-center bg-slate-900/80 hover:bg-slate-800/90 text-white rounded-lg border border-slate-700/50 backdrop-blur-md transition-all duration-200 focus-visible:ring-2 focus-visible:ring-violet-400 outline-none hover:shadow-xl">
              <span className="sr-only">Close</span>
              <svg width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12"/></svg>
            </button>
            
            {!imageError && displayUrl ? (
              <>
                <Image src={displayUrl} alt={`Generated image in ${styleDisplayName} style`} className="rounded-2xl shadow-2xl object-contain transition-opacity duration-500 max-w-full max-h-[80vh] w-full h-auto" width={900} height={900} style={{ objectFit: "contain", imageRendering: "crisp-edges", backfaceVisibility: "hidden", transform: "translateZ(0)", willChange: "auto", maxWidth: '100%', maxHeight: '80vh' }} onLoad={() => setImageLoaded(true)} onError={() => setImageLoaded(false)} loading="lazy" />
                
                {showActions && imageLoaded && (
                  <div className="absolute bottom-3 right-3 z-30 flex flex-row gap-2 items-center">
                    {user && !isOwnImage && (
                      <TooltipProvider delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button variant="ghost" size="icon" onClick={handleLikeToggle} disabled={isLiking || isLoadingLikeStatus} className={cn("h-8 w-8 backdrop-blur-md border transition-all duration-200 group/likebutton", userLiked ? "bg-pink-600/90 text-white border-pink-500/50 hover:bg-pink-500/90 hover:border-pink-400/50" : "bg-slate-900/90 text-slate-200 border-slate-700/50 hover:bg-slate-800/90 hover:border-slate-600/50 hover:text-white")} aria-label={userLiked ? "Unlike image" : "Like image"}>
                              <Heart className={cn("h-4 w-4 transition-all group-hover/likebutton:scale-110", userLiked ? "fill-current" : "fill-none")} />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent sideOffset={6}>{userLiked ? "Unlike" : "Like"}<TooltipArrow className="fill-slate-900/70" /></TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                    {onShare && (
                      <TooltipProvider delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button variant="ghost" size="icon" onClick={handleCommunityShare} className={cn("h-8 w-8 backdrop-blur-md border transition-all duration-200", optimisticIsShared ? "bg-emerald-900/90 text-emerald-200 border-emerald-700/50 hover:bg-emerald-800/90 hover:border-emerald-600/50" : "bg-slate-900/90 text-slate-200 border-slate-700/50 hover:bg-slate-800/90 hover:border-slate-600/50 hover:text-white")} aria-label={optimisticIsShared ? `Unshare ${styleDisplayName} image` : `Share ${styleDisplayName} image`}>
                              <Share2 className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent sideOffset={6}>{optimisticIsShared ? "Unshare from Community" : "Share in Community"}<TooltipArrow className="fill-slate-900/70" /></TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                    <SocialSharePopover
                      url={`${typeof window !== 'undefined' ? window.location.origin : ''}/images/${image.id}`}
                      title={`AI artwork in ${styleDisplayName} style`}
                    />
                    <TooltipProvider delayDuration={100}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" onClick={handleDownload} disabled={isDownloading} className="h-8 w-8 bg-slate-900/90 backdrop-blur-md text-slate-200 border border-slate-700/50 hover:bg-slate-800/90 hover:border-slate-600/50 hover:text-white transition-all duration-200 text-xs" aria-label={`Download ${styleDisplayName} image`}>
                            {isDownloading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent sideOffset={6}>Download<TooltipArrow className="fill-slate-900/70" /></TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    {onDelete && (
                      <TooltipProvider delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button variant="ghost" size="icon" onClick={() => setIsDeleteModalOpen(true)} disabled={isDeleting} className="h-8 w-8 bg-slate-900/90 backdrop-blur-md text-red-400 border border-red-800/50 hover:bg-red-900/50 hover:border-red-700/50 hover:text-red-300 disabled:opacity-50 transition-all duration-200" aria-label={`Delete ${styleDisplayName} image`}>
                              {isDeleting ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent sideOffset={6}>Delete<TooltipArrow className="fill-slate-900/70" /></TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                )}
              </>
            ) : null}
            
            {isLoading && (<div className="w-full h-96 flex items-center justify-center"><div className="h-24 w-24 rounded-xl bg-slate-800 animate-pulse" /></div>)}
            
          </div>
        </DialogContent>
      </Dialog>
      <DynamicDeleteConfirmationModal isOpen={isDeleteModalOpen} onClose={() => setIsDeleteModalOpen(false)} onConfirm={handleDeleteConfirm} isDeleting={isDeleting} itemName={`${styleDisplayName} artwork`} description={`This will permanently delete your ${styleDisplayName} artwork. The image file will be removed from secure storage and all metadata will be deleted from the database.`} />
    </>
  );
}
