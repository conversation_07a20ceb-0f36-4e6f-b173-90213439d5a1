import React from "react";

/**
 * StudioColumns: Two-column layout for Studio page.
 * - Left: main content (upload, style, etc.)
 * - Right: navbar (recent creations)
 * Uses <main> and <aside> for semantic HTML, and <section> for logical content blocks.
 */
export function StudioColumns({ main, navbar }: { main: React.ReactNode; navbar: React.ReactNode }) {
  return (
    <div className="flex flex-col lg:flex-row items-start w-full gap-4">
      <main className="flex flex-col w-full min-w-0 items-stretch" aria-label="Main content">
        {main}
      </main>
      {/* Hidden on mobile/medium, visible and responsive on desktop */}
      <aside 
        className="hidden lg:block w-auto min-w-[240px] max-w-[340px] flex-shrink-0 transition-all duration-200" 
        style={{ flex: '0 1 28%' }}
        aria-label="Recent creations navbar"
      >
        <section className="sticky top-4 w-full">{navbar}</section>
      </aside>
    </div>
  );
} 