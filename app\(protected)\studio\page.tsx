"use client";
export const dynamic = "force-dynamic";
// Route: /studio
// Studio Page (protected)
// Main AI image generation interface. Requires authentication.

import { useUser } from "@/lib/contexts/UserContext";
import { useRecentCreations } from "@/lib/hooks/useRecentCreations";
import { useImageSharing } from "@/lib/hooks/useImageSharing";
import { SupabaseService } from "@/lib/supabase";
import { StudioHero } from "./StudioHero";
import { GenerationControls } from "@/components/studio/generation-controls";
import { RecentCreations } from "@/components/studio/recent-creations";
import { RecentlyUsedStylesProvider } from "@/lib/contexts/RecentlyUsedStylesContext";
import { StudioColumns } from "@/components/layout/StudioColumns";
import React, { useEffect } from "react";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { toast } from "sonner";
import { useStudioLogic } from "./useStudioLogic";
import { User, GeneratedImage } from "@/lib/types";
import { useImageDelete } from "@/lib/hooks/useImageDelete";

type StudioPageInnerProps = {
  user: any; // Accept Supabase user type for compatibility
  recentImages: GeneratedImage[];
  loadingRecentImages: boolean;
  refreshRecentImages: () => void;
  handleImageShare: (imageId: string, isShared: boolean) => Promise<boolean>;
  SupabaseService: typeof import("@/lib/supabase").SupabaseService;
};

function StudioPageInner({ user, handleImageShare, SupabaseService }: Omit<StudioPageInnerProps, 'recentImages' | 'loadingRecentImages' | 'refreshRecentImages'>) {
  const studioLogic = useStudioLogic();
  const { deleteImage } = useImageDelete();

  const handleShare = async (imageId: string, isShared: boolean) => {
    await handleImageShare(imageId, isShared);
    studioLogic.refreshRecentImages();
  };

  const handleDelete = async (imageId: string) => {
    const imageToDelete = studioLogic.recentImages.find(img => img.id === imageId);
    if (imageToDelete) {
      await deleteImage(imageId, imageToDelete);
      studioLogic.refreshRecentImages();
    }
  };

  if (studioLogic.isLoading) {
    return <div className="flex items-center justify-center min-h-screen"><span>Loading...</span></div>;
  }

  return (
    <StudioColumns
      main={[
        <StudioHero key="hero" />, // unchanged
        <GenerationControls
          key="controls"
          user={user as any}
          isApiConfigured={studioLogic.isApiConfigured}
          isOnline={studioLogic.clientIsOnline}
          isGenerating={studioLogic.isGenerating}
          generationProgress={studioLogic.generationProgress}
          selectedStyle={studioLogic.selectedStyle}
          uploadedImage={studioLogic.uploadedImage}
          customPrompt={studioLogic.customPrompt}
          hasUsedCurrentImage={studioLogic.hasUsedCurrentImage}
          onStyleSelect={studioLogic.setSelectedStyle}
          onUploadedImageSelect={studioLogic.setUploadedImage}
          onCustomPromptChange={studioLogic.setCustomPrompt}
          onGenerate={studioLogic.handleGenerateImage}
        />
      ]}
      navbar={
        <RecentCreations
          recentImages={studioLogic.recentImages}
          onShare={handleShare}
          onDelete={handleDelete}
          loading={!studioLogic.hasLoadedRecentImages}
        />
      }
    />
  );
}

export default function StudioPage() {
  const { user, isLoading } = useUser();
  const { handleImageShare } = useImageSharing();

  useEffect(() => {
    if (
      user &&
      typeof window !== "undefined" &&
      window.sessionStorage.getItem("showWelcomeToast") === "1"
    ) {
      toast.success("🎨 Welcome back, creator!");
      window.sessionStorage.removeItem("showWelcomeToast");
    }
  }, [user]);

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen"><span>Loading...</span></div>;
  }

  if (!user) {
    // Not authenticated, ProtectedRoute will handle redirect, or just return null
    return null;
  }

  return (
    <ProtectedRoute>
      <div className="animate-studio-fadein">
        <RecentlyUsedStylesProvider userId={user.id}>
          <StudioPageInner
            user={user}
            handleImageShare={handleImageShare}
            SupabaseService={SupabaseService}
          />
        </RecentlyUsedStylesProvider>
      </div>
    </ProtectedRoute>
  );
}
