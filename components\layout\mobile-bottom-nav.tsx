"use client";

import React, { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { 
  Palette,
  Image, 
  Users, 
  User
} from "lucide-react";
import { useUser } from "@/lib/contexts/UserContext";

interface NavItem {
  path: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
}

const navItems: NavItem[] = [
  {
    path: "/studio",
    label: "Studio",
    icon: Palette,
    gradient: "from-violet-500 to-purple-500"
  },
  {
    path: "/gallery",
    label: "Gallery",
    icon: Image,
    gradient: "from-violet-500 to-blue-500"
  },
  {
    path: "/community",
    label: "Community",
    icon: Users,
    gradient: "from-emerald-500 to-teal-500"
  },
  {
    path: "/profile",
    label: "Profile",
    icon: User,
    gradient: "from-violet-500 to-blue-500"
  }
];

// NOTE: MobileBottomNav uses useUser and should only be used in protected layouts/routes.
// If you need a public mobile nav, create a separate PublicMobileBottomNav component without useUser.
export function MobileBottomNav() {
  const pathname = usePathname();
  const router = useRouter();
  const { user, isLoading } = useUser();
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  // Hide navigation if user is not logged in or component has not mounted
  if (!hasMounted || isLoading || !user) return null;

  return (
    <nav
      role="navigation"
      aria-label="Main mobile navigation"
      className="fixed bottom-0 left-0 right-0 z-50 md:hidden mobile-bottom-nav"
    >
      <div className="glass backdrop-blur-xl w-full overflow-hidden">
        <div className="flex items-center justify-between py-2 px-1 w-full">
          {navItems.map(({ icon: Icon, label, path, gradient }) => {
            const isActive = pathname === path;
            
            return (
              <button
                key={path}
                onClick={() => router.push(path)}
                aria-current={isActive ? "page" : undefined}
                className={cn(
                  "flex flex-col items-center space-y-1 px-0 py-1 transition-all duration-200 nav-transition group touch-manipulation flex-1 min-w-0",
                  "focus:outline-none focus:ring-2 focus:ring-slate-400/20 focus:ring-offset-2 focus:ring-offset-slate-900",
                  "border-none bg-transparent",
                  isActive
                    ? "text-white"
                    : "text-slate-400 hover:text-white active:text-white"
                )}
                style={{
                  border: 'none',
                  background: 'transparent',
                  outline: 'none',
                  boxShadow: 'none'
                }}
              >
                <div
                  className={cn(
                    "relative p-2 rounded-lg transition-all duration-200 flex-shrink-0",
                    "group-active:scale-95",
                    isActive
                      ? `bg-gradient-to-r ${gradient} shadow-lg`
                      : "bg-slate-700/80 border border-slate-600/50"
                  )}
                  style={{
                    border: 'none'
                  }}
                >
                  {isActive && (
                    <div
                      className={cn(
                        "absolute inset-0 bg-gradient-to-r rounded-lg blur-lg opacity-50",
                        gradient
                      )}
                    ></div>
                  )}
                  <Icon
                    className={cn(
                      "h-5 w-5 relative z-10 transition-transform duration-200 text-white",
                      isActive && "scale-110"
                    )}
                  />
                </div>
                <span
                  className={cn(
                    "text-xs font-medium transition-all duration-200 text-center leading-tight truncate w-full",
                    isActive
                      ? "text-white font-semibold"
                      : "text-slate-400 group-hover:text-slate-300 group-active:text-white"
                  )}
                >
                  {label}
                </span>
              </button>
            );
          })}
        </div>
      </div>
    </nav>
  );
} 
