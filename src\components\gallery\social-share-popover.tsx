
"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { getFacebookShareUrl, getTwitterShareUrl } from "@/lib/utils/format";
import { useImageShare } from "@/hooks/useImageShare";
import { Copy, Facebook, Send, Twitter } from "lucide-react";
import { useState } from "react";

interface SocialSharePopoverProps {
  url: string;
  title: string;
}

export function SocialSharePopover({ url, title }: SocialSharePopoverProps) {
  const { copyToClipboard, isCopying } = useImageShare();
  const [isOpen, setIsOpen] = useState(false);

  const handleCopy = () => {
    copyToClipboard(url);
    setIsOpen(false);
  };

  const twitterUrl = getTwitterShareUrl(url, title);
  const facebookUrl = getFacebookShareUrl(url);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          aria-label="Share to Socials"
          className="h-8 w-8 bg-background/90 backdrop-blur-md text-primary border-primary/50 hover:bg-accent hover:text-accent-foreground transition-all duration-200"
          onClick={(e) => e.stopPropagation()}
        >
          <Send className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-2" onClick={(e) => e.stopPropagation()}>
        <div className="grid gap-2">
          <div className="text-center text-sm font-medium text-muted-foreground pb-1">
            Share via
          </div>
          <Separator />
          <Button
            variant="ghost"
            className="w-full justify-start gap-2"
            asChild
          >
            <a href={twitterUrl} target="_blank" rel="noopener noreferrer" onClick={() => setIsOpen(false)}>
              <Twitter className="h-4 w-4" />
              Twitter / X
            </a>
          </Button>
          <Button
            variant="ghost"
            className="w-full justify-start gap-2"
            asChild
          >
            <a href={facebookUrl} target="_blank" rel="noopener noreferrer" onClick={() => setIsOpen(false)}>
              <Facebook className="h-4 w-4" />
              Facebook
            </a>
          </Button>
          <Separator />
          <Button
            variant="ghost"
            className="w-full justify-start gap-2"
            onClick={handleCopy}
            disabled={isCopying}
          >
            <Copy className="h-4 w-4" />
            {isCopying ? 'Copying...' : 'Copy Link'}
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
