
"use client";

import { MemoizedUnifiedImageCard } from "@/components/gallery/unified-image-card";
import UnifiedLoader from "@/components/ui/unified-loader";
import { GeneratedImage } from "@/lib/types";
import React, { useRef, useEffect, useState, useCallback, useMemo, Suspense } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface ImageGridProps {
  images: GeneratedImage[];
  isLoading?: boolean;
  error?: any;
  loadMore?: () => void;
  hasMore?: boolean;
  onShare?: (image: GeneratedImage) => ((imageId: string, isShared: boolean) => void) | undefined;
  onDelete?: (imageId: string) => void;
  showDownloadButton?: boolean;
  showSharedChip?: boolean | ((image: GeneratedImage) => boolean);
  hideLikeButton?: boolean | ((image: GeneratedImage) => boolean);
  columns?: number;
  cardVariant?: "default" | "compact";
}

export const ImageGrid: React.FC<ImageGridProps> = React.memo(
  ({
    images,
    isLoading = false,
    error,
    loadMore,
    hasMore,
    onShare,
    onDelete,
    showDownloadButton = false,
    showSharedChip = false,
    hideLikeButton = false,
    columns = 4,
    cardVariant = "default",
  }) => {
    const loaderRef = useRef<HTMLDivElement>(null);
    const [showEmpty, setShowEmpty] = useState(false);
    const [lastNonEmptyImages, setLastNonEmptyImages] = useState<GeneratedImage[]>([]);

    useEffect(() => {
      if (Array.isArray(images) && images.length > 0) {
        setLastNonEmptyImages(images);
      }
    }, [images]);

    // More robust deduplication with stable reference
    const dedupedImages = useMemo(() => {
      // If loading, show the last known good set of images to prevent flicker
      const source = isLoading && lastNonEmptyImages.length > 0 ? lastNonEmptyImages :
        (Array.isArray(images) && images.length > 0) ? images : lastNonEmptyImages;

      if (!Array.isArray(source) || source.length === 0) return [];
      
      const seen = new Set<string>();
      const result: GeneratedImage[] = [];
      for (const img of source) {
        if (img.id && !seen.has(img.id)) {
          seen.add(img.id);
          result.push(img);
        }
      }
      return result;
    }, [images, lastNonEmptyImages, isLoading]);

    // Stable empty state handling
    const shouldShowEmpty = useMemo(() => 
      dedupedImages.length === 0 && !isLoading, 
      [dedupedImages.length, isLoading]
    );

    useEffect(() => {
      setShowEmpty(shouldShowEmpty);
    }, [shouldShowEmpty]);

    // Infinite scroll with stable observer
    useEffect(() => {
      if (!loadMore || !hasMore || isLoading) return;
      
      const currentLoader = loaderRef.current;
      if (!currentLoader) return;

      const observer = new window.IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && !isLoading && hasMore) {
            loadMore();
          }
        },
        { rootMargin: "200px" }
      );
      
      observer.observe(currentLoader);
      return () => observer.unobserve(currentLoader);
    }, [loadMore, hasMore, isLoading]);

    // Responsive grid columns class
    const gridColsClass = useMemo(() => {
      // For the Recent Creations sidebar (columns=1), use a single column that takes full width
      if (columns === 1) return "grid-cols-1";
      
      // For other cases, use responsive columns
      return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4";
    }, [columns]);

    // Memoize card props to prevent unnecessary re-renders
    const getCardProps = useCallback((image: GeneratedImage) => ({
      cardOnShare: onShare ? onShare(image) : undefined,
      cardHideLikeButton: typeof hideLikeButton === 'function' ? hideLikeButton(image) : hideLikeButton,
      cardShowSharedChip: typeof showSharedChip === 'function' ? showSharedChip(image) : showSharedChip,
    }), [onShare, hideLikeButton, showSharedChip]);

    // Now, after all hooks, handle early return for error
    if (error) {
      return (
        <div className="flex items-center justify-center py-12 text-red-500">
          Failed to load images.
        </div>
      );
    }

    return (
      <>
        <div className={`grid ${gridColsClass} gap-4 min-h-[200px] w-full`}>
          <AnimatePresence>
            {dedupedImages.map((image: GeneratedImage, i) => {
              const { cardOnShare, cardHideLikeButton, cardShowSharedChip } = getCardProps(image);
              return (
                <motion.div
                  key={image.id}
                  layout
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  transition={{ 
                    type: "spring",
                    stiffness: 400,
                    damping: 40,
                  }}
                  className="image-grid-item"
                >
                  <MemoizedUnifiedImageCard
                    image={image}
                    showActions={true}
                    variant={cardVariant}
                    onShare={cardOnShare}
                    onDelete={onDelete}
                    showDownloadButton={showDownloadButton}
                    showSharedChip={cardShowSharedChip}
                    hideLikeButton={cardHideLikeButton}
                    priority={i < 4}
                    loading={i < 8 ? "eager" : "lazy"}
                  />
                </motion.div>
              );
            })}
          </AnimatePresence>
          {/* Empty state */}
          {shouldShowEmpty && showEmpty && (
            <div className="col-span-full flex items-center justify-center py-12">
              <div className="text-center">
                <div className="text-slate-400 text-lg mb-2">
                  No images found
                </div>
                <div className="text-slate-500 text-sm">
                  Try adjusting your filters or create some new images
                </div>
              </div>
            </div>
          )}
        </div>
        {/* Infinite scroll loader */}
        {dedupedImages.length > 0 && isLoading && hasMore && (
          <div 
            ref={loaderRef} 
            className="flex items-center justify-center py-6"
          >
            <UnifiedLoader text="Loading more..." />
          </div>
        )}
      </>
    );
  }
);
ImageGrid.displayName = "ImageGrid";

export type { ImageGridProps };
