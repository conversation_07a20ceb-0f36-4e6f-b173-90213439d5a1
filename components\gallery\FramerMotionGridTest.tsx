"use client";
import React, { useState, Suspense } from "react";

const AnimatePresence = React.lazy(() => import('framer-motion').then(mod => ({ default: mod.AnimatePresence })));
const MotionDiv = React.lazy(() => import('framer-motion').then(mod => ({ default: mod.motion.div })));

const COLORS = [
  "#e57373", "#f06292", "#ba68c8", "#64b5f6", "#4db6ac", "#81c784", "#ffd54f", "#ffb74d", "#a1887f"
];

export default function FramerMotionGridTest() {
  const [boxes, setBoxes] = useState(
    COLORS.map((color, i) => ({ id: String(i), color }))
  );

  return (
    <div>
      <h2 className="mb-4 text-lg font-bold">Framer Motion Grid Test</h2>
      <div className="grid grid-cols-3 gap-4 max-w-md">
        <Suspense fallback={null}>
        <AnimatePresence mode="popLayout">
          {boxes.map((box) => (
              <MotionDiv
              key={box.id}
              layout
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95, y: 24 }}
              transition={{ duration: 0.3 }}
              className="rounded-lg shadow-lg flex flex-col items-center justify-center h-24 cursor-pointer"
              style={{ background: box.color }}
              onClick={() => setBoxes((prev) => prev.filter((b) => b.id !== box.id))}
              title="Click to remove"
            >
              <span className="text-white font-bold text-xl">{box.id}</span>
              <span className="text-xs text-white mt-2">Click to remove</span>
              </MotionDiv>
          ))}
        </AnimatePresence>
        </Suspense>
      </div>
      {boxes.length === 0 && (
        <div className="mt-6 text-center text-slate-500">All boxes removed!</div>
      )}
    </div>
  );
} 