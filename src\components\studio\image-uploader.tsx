
"use client";

import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import {
  Upload,
  X,
  Image as ImageIcon,
  Wand2,
  AlertCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { IMAGE_GENERATION_CONFIG } from "@/lib/constants";
import { Card } from "../ui/card";

interface ImageUploaderProps {
  onImageSelect: (file: File | null) => void;
  selectedImage: File | null;
  hasBeenUsed?: boolean;
}

export function ImageUploader({
  onImageSelect,
  selectedImage,
  hasBeenUsed = false,
}: ImageUploaderProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [objectUrl, setObjectUrl] = useState<string | null>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const [isFileDialogOpen, setIsFileDialogOpen] = useState(false);
  const lastDialogOpenTime = useRef(0);

  // Reset dialog open state when window regains focus (file dialog closes)
  useEffect(() => {
    const handleWindowFocus = () => {
      setIsFileDialogOpen(false);
    };
    window.addEventListener("focus", handleWindowFocus);
    return () => {
      window.removeEventListener("focus", handleWindowFocus);
    };
  }, []);

  useEffect(() => {
    if (selectedImage) {
      const url = URL.createObjectURL(selectedImage);
      setObjectUrl(url);
      setImageLoaded(false);
      return () => {
        URL.revokeObjectURL(url);
      };
    } else {
      setObjectUrl(null);
      setImageLoaded(false);
    }
  }, [selectedImage]);

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find((file) =>
      IMAGE_GENERATION_CONFIG.allowedFileTypes.includes(file.type),
    );
    if (imageFile && imageFile.size <= IMAGE_GENERATION_CONFIG.maxFileSize) {
      onImageSelect(imageFile);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsFileDialogOpen(false);
    const file = e.target.files?.[0];
    if (file && file.size <= IMAGE_GENERATION_CONFIG.maxFileSize) {
      onImageSelect(file);
    }
    // No longer resetting input value here to prevent re-opening dialog issue
  };

  const handleRemoveImage = () => {
    onImageSelect(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleOpenFileDialog = () => {
    const now = Date.now();
    // Debounce: prevent opening again within 500ms
    if (isFileDialogOpen && now - lastDialogOpenTime.current < 500) {
      return;
    }
    setIsFileDialogOpen(true);
    lastDialogOpenTime.current = now;
    fileInputRef.current?.click();
  };

  if (selectedImage && objectUrl) {
    return (
      <div className="relative">
        <Card
          ref={imageContainerRef}
          className="relative w-full aspect-video rounded-xl overflow-hidden flex items-center justify-center"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <Image
            src={objectUrl}
            alt="Selected image for AI transformation"
            width={0}
            height={0}
            sizes="100vw"
            style={{ objectFit: "contain" }}
            className={`transition-opacity duration-500 w-full h-auto max-h-full ${imageLoaded ? "opacity-100" : "opacity-0"}`}
            onLoad={handleImageLoad}
          />
          <button
            type="button"
            className={`absolute top-3 left-3 z-10 bg-destructive hover:bg-destructive/90 text-destructive-foreground rounded-full p-2 shadow-lg transition-all duration-200 focus-visible:ring-2 focus-visible:ring-destructive outline-none hover:shadow-xl ${isHovered ? "opacity-100" : "opacity-0 pointer-events-none"}`}
            onClick={handleRemoveImage}
            aria-label="Remove selected image"
          >
            <X className="h-4 w-4" />
          </button>
          <button
            type="button"
            className={`absolute top-3 right-3 z-10 bg-background/80 hover:bg-primary text-primary-foreground rounded-full p-2 border border-border/20 shadow-lg transition-all duration-200 focus-visible:ring-2 focus-visible:ring-primary outline-none hover:shadow-xl hover:border-primary/50 ${isHovered ? "opacity-100" : "opacity-80"}`}
            onClick={(e) => {
              e.stopPropagation();
              handleOpenFileDialog();
            }}
            aria-label="Change picture"
          >
            <ImageIcon className="h-4 w-4" />
          </button>
          <div
            className="absolute bottom-4 left-1/2 -translate-x-1/2 z-20 flex items-center gap-1.5 px-3 py-1 rounded-full text-xs font-medium bg-primary/20 text-primary border border-primary/30"
          >
            <Wand2 className="h-4 w-4 mr-1 opacity-80" />
            <span>
              {hasBeenUsed
                ? "Ready for more transformations • Try different styles"
                : "Ready for AI transformation • Try different styles"}
            </span>
          </div>
        </Card>
        <input
          ref={fileInputRef}
          type="file"
          accept={IMAGE_GENERATION_CONFIG.allowedFileTypes.join(",")}
          onChange={handleFileSelect}
          className="hidden"
          name="image-upload"
        />
      </div>
    );
  }

  return (
    <div
      className={cn(
        "border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 cursor-pointer group",
        isDragOver
          ? "border-primary bg-primary/10"
          : "border-border hover:border-primary/50 hover:bg-primary/5",
      )}
      onClick={handleOpenFileDialog}
      tabIndex={0}
      role="button"
      aria-label="Upload image"
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <div className="flex flex-col items-center space-y-4">
        <div
          className={cn(
            "relative p-6 rounded-full transition-all duration-300",
            isDragOver
              ? "bg-primary text-primary-foreground"
              : "bg-secondary group-hover:bg-accent",
          )}
        >
          {isDragOver && (
            <div className="absolute inset-0 bg-primary rounded-full blur-xl opacity-50"></div>
          )}
          <Wand2 className={cn(
            "h-8 w-8 relative z-10 transition-colors duration-300",
            isDragOver ? "text-primary-foreground" : "text-muted-foreground group-hover:text-accent-foreground"
          )} />
        </div>

        <div className="space-y-3">
          <div className="space-y-1">
            <p
              className={cn(
                "text-lg font-semibold transition-colors duration-300",
                isDragOver
                  ? "text-primary"
                  : "text-foreground group-hover:text-foreground",
              )}
            >
              {isDragOver ? "Drop your image here" : "Upload Your Image"}
            </p>
            <p className="text-sm text-muted-foreground group-hover:text-muted-foreground transition-colors duration-300">
              Drag & drop or click to select an image to use for your artwork
            </p>
          </div>

          <div className="text-xs text-muted-foreground space-y-1">
            <p>Supported: PNG, JPG, GIF</p>
            <p>Maximum size: 4MB</p>
          </div>
        </div>

        <Button
          onClick={handleOpenFileDialog}
          className="mx-auto"
          aria-label="Select Image to Transform"
        >
          <Upload className="h-5 w-5 mr-2 -ml-1" />
          Select Image to Transform
        </Button>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept={IMAGE_GENERATION_CONFIG.allowedFileTypes.join(",")}
        onChange={handleFileSelect}
        className="hidden"
        name="image-upload"
      />
    </div>
  );
}
