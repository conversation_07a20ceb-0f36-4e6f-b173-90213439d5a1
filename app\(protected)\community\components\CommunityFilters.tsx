import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

// Define CommunityFiltersProps interface for props
interface CommunityFiltersProps {
  styleFilter: string;
  setStyleFilter: (s: string) => void;
  styles: Array<{ id: string; name: string; prompt?: string }>;
  sortOrder: string;
  setSortOrder: (s: string) => void;
}

export function CommunityFilters({
  styleFilter,
  setStyleFilter,
  styles,
  sortOrder,
  setSortOrder,
}: CommunityFiltersProps) {
  // Defensive: always ensure options are available
  const styleOptions = ["all", ...styles.map((style: { id: string }) => style.id)];
  const safeStyleFilter = styleOptions.includes(styleFilter)
    ? styleFilter
    : "all";
  const sortOptions = ["recent", "trending"];
  const safeSortOrder = sortOptions.includes(sortOrder)
    ? sortOrder
    : "recent";

  // Get display names for selected values
  const getStyleDisplayName = (filter: string) => {
    if (filter === "all") return "All Styles";
    const style = styles.find((s) => s.id === filter);
    return style ? style.name : "All Styles";
  };

  const getSortDisplayName = (order: string) => {
    switch (order) {
      case "recent":
        return "Most Recent";
      case "trending":
        return "Trending";
      default:
        return "Most Recent";
    }
  };

  // Handle filter changes with smooth transitions
  const handleStyleChange = (value: string) => {
    if (value && value !== safeStyleFilter) {
      setStyleFilter(value);
    }
  };

  const handleSortChange = (value: string) => {
    if (value && value !== safeSortOrder) {
      setSortOrder(value as "recent" | "trending");
    }
  };
  const triggerClasses = "w-full h-9 bg-slate-800/50 border-slate-700 text-white hover:bg-slate-700/50 transition-colors duration-200 text-sm";
  const contentClasses = "bg-slate-800 border-slate-700 text-white";
  const itemClasses = "hover:bg-slate-700 focus:bg-slate-700 data-[highlighted]:bg-slate-700 data-[state=checked]:bg-violet-600";

  return (
    <div className="flex flex-col sm:flex-row gap-2 stagger-animation">
      {/* Style Filter */}
      <div className="w-48">
        <Select value={safeStyleFilter} onValueChange={handleStyleChange}>
          <SelectTrigger className={cn(triggerClasses, "data-[state=open]:bg-slate-700/80")}>
            <SelectValue placeholder="All Styles">
              {getStyleDisplayName(safeStyleFilter)}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className={contentClasses}>
            <SelectItem value="all" className={itemClasses}>
              All Styles
            </SelectItem>
            {styles.map((style) => (
              <SelectItem
                key={style.id}
                value={style.id}
                className={itemClasses}
              >
                {style.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Sort Order Filter */}
      <div className="w-48">
        <Select value={safeSortOrder} onValueChange={handleSortChange}>
          <SelectTrigger className={cn(triggerClasses, "data-[state=open]:bg-slate-700/80")}>
            <SelectValue placeholder="Most Recent">
              {getSortDisplayName(safeSortOrder)}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className={contentClasses}>
            <SelectItem value="recent" className={itemClasses}>
              Most Recent
            </SelectItem>
            <SelectItem value="trending" className={itemClasses}>
              Trending
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
