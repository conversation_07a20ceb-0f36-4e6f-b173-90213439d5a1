
"use client";

import { usePathname, useRouter } from "next/navigation";
import { Palette, Image, Users, User, LogOut, Wand2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { SupabaseService } from "@/lib/supabase";
import { toast } from "sonner";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipArrow,
} from "@/components/ui/tooltip";
import { useAnalytics } from "@/lib/hooks/useAnalytics";
import { showAuthToast } from "@/lib/utils/toast";
import { useUser } from "@/lib/contexts/UserContext";

const navItems = [
  {
    icon: Palette,
    label: "Studio",
    path: "/studio",
    gradient: "from-violet-500 to-purple-500",
  },
  {
    icon: Image,
    label: "Gallery",
    path: "/gallery",
    gradient: "from-blue-500 to-cyan-500",
  },
  {
    icon: Users,
    label: "Community",
    path: "/community",
    gradient: "from-emerald-500 to-teal-500",
  },
];

// NOTE: Navbar uses useUser and should only be used in protected layouts/routes.
// If you need a public navbar, create a separate PublicNavbar component without useUser.
export function Navbar() {
  const pathname = usePathname();
  const router = useRouter();
  const { trackUserLogout } = useAnalytics();
  const { user, isLoading } = useUser();

  if (isLoading || !user) return null;

  return (
    <>
      {/* Desktop Left Navigation - Fixed and Sticky */}
      <nav
        role="navigation"
        aria-label="Main desktop navigation"
        className="hidden md:block sticky top-0 h-screen z-50 flex-shrink-0"
      >
        <div className="w-16 h-screen glass-card !border-none flex flex-col">
          {/* Logo at the top */}
          <div className="flex flex-col items-center pt-6 pb-2">
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <div className="h-12 flex items-center justify-center">
                  <TooltipTrigger asChild>
                    <button
                      onClick={() => router.push("/")}
                      className="w-12 h-12 rounded-full flex items-center justify-center mb-0 shadow-lg hover:scale-105 transition-transform"
                      aria-label="Go to Landing Page"
                      style={{ background: "none" }}
                    >
                      <div className="relative flex items-center justify-center">
                        <div className="absolute -top-2 -left-2 w-14 h-14 bg-gradient-to-br from-violet-500 to-blue-500 rounded-full blur-xl opacity-50 animate-gradient-move animate-pulse"></div>
                        <div
                          className="relative z-10 p-2 bg-gradient-to-br from-violet-600 to-blue-500 rounded-full border-2 border-white/20 animate-gradient-move"
                          style={{
                            animation: "gradient-move 6s ease-in-out infinite"
                          }}
                        >
                          <Wand2 className="h-6 w-6 text-white drop-shadow-xl" />
                        </div>
                      </div>
                    </button>
                  </TooltipTrigger>
                  <TooltipContent sideOffset={12} side="right" align="center">
                    Go to Landing Page
                    <TooltipArrow className="fill-slate-900/70" />
                  </TooltipContent>
                </div>
              </Tooltip>
            </TooltipProvider>
          </div>
          {/* Main Navigation - Centered */}
          <div className="flex-1 flex flex-col items-center justify-center space-y-3 h-full">
            {navItems.map(({ icon: Icon, label, path, gradient }) => {
              const isActive = pathname === path;
              return (
                <TooltipProvider delayDuration={100} key={path}>
                  <Tooltip>
                    <div className="h-12 flex items-center justify-center">
                      <TooltipTrigger asChild>
                        <button
                          onClick={() => router.push(path)}
                          aria-current={isActive ? "page" : undefined}
                          className={cn(
                            "w-10 h-10 flex items-center justify-center rounded-lg transition-all duration-300 nav-transition shadow-lg focus-visible:ring-2 focus-visible:ring-blue-400 outline-none group",
                            isActive
                              ? `bg-gradient-to-r ${gradient} text-white scale-110`
                              : "bg-slate-800/50 text-slate-400 hover:text-white hover:scale-105 hover:bg-slate-700/50",
                            "hover:shadow-[0_0_0_4px_rgba(124,58,237,0.15)] group-focus-visible:shadow-[0_0_0_4px_rgba(37,99,235,0.25)]",
                          )}
                          aria-label={label}
                        >
                          <Icon className="h-6 w-6" />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent sideOffset={12} side="right" align="center">
                        {label}
                        <TooltipArrow className="fill-slate-900/70" />
                      </TooltipContent>
                    </div>
                  </Tooltip>
                </TooltipProvider>
              );
            })}
          </div>
          {/* Profile Section - Bottom */}
          <div className="p-4">
            <div className="flex flex-col items-center space-y-3">
              {/* Profile Button */}
              <TooltipProvider delayDuration={100}>
                <Tooltip>
                  <div className="h-12 flex items-center justify-center">
                    <TooltipTrigger asChild>
                      <button
                        onClick={() => router.push("/profile")}
                        className="w-10 h-10 flex items-center justify-center rounded-lg text-white transition relative group focus-visible:ring-2 focus-visible:ring-blue-400 outline-none hover:shadow-[0_0_0_4px_rgba(124,58,237,0.15)] group-focus-visible:shadow-[0_0_0_4px_rgba(37,99,235,0.25)]"
                        aria-label="Profile"
                      >
                        <span className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition bg-gradient-to-r from-violet-500 to-blue-500 z-0" />
                        <User className="h-6 w-6 text-slate-400 group-hover:text-white transition-colors z-10 relative" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent sideOffset={12} side="right" align="center">
                      Profile
                      <TooltipArrow className="fill-slate-900/70" />
                    </TooltipContent>
                  </div>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </nav>
      <style jsx global>{`
        @keyframes float {
          0% { transform: translateY(0); }
          50% { transform: translateY(-8px); }
          100% { transform: translateY(0); }
        }
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
        @keyframes gradient-move {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        .animate-gradient-move {
          background: linear-gradient(270deg, #7c3aed, #2563eb, #06b6d4, #7c3aed);
          background-size: 400% 400%;
          animation: gradient-move 6s ease-in-out infinite;
        }
      `}</style>
    </>
  );
}

    
