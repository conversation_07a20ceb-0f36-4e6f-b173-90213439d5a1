// Route: /
// Landing Page (public)
// This is the main public homepage for PxlMorph.
// Features branding, CTA, and highlights app features.
// No authentication required.
'use client';
export const dynamic = "force-dynamic";

import React, { Suspense } from 'react';
import { Motion } from '@/lib/utils/dynamicImports';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { Button } from "@/components/ui/button";
import { Wand2 } from "lucide-react";
import { createBrowserClient } from '@supabase/ssr';
import { useRouter } from "next/navigation";
import AppBackground from "@/components/layout/BackgroundWrapper";

// Type for feature items
interface Feature {
  icon: string;
  title: string;
  description: string;
}

const features: Feature[] = [
  {
    icon: '✨',
    title: 'AI-Powered',
    description: 'Cutting-edge AI technology'
  },
  {
    icon: '🎨',
    title: 'Endless Styles',
    description: 'Multiple art styles'
  },
  {
    icon: '⚡',
    title: 'Lightning Fast',
    description: 'Quick generations'
  }
];

export default function LandingPage() {
  const [isMounted, setIsMounted] = useState(false);
  const [user, setUser] = useState<{ id: string } | null>(null);
  const router = useRouter();

  useEffect(() => {
    setIsMounted(true);
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
    });
  }, []);

  if (!isMounted) return null;

  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center text-white overflow-hidden">
      <AppBackground />
      <div className="w-full max-w-4xl p-6 relative z-10">
        {/* Animated Background */}
        <div className="fixed inset-0 overflow-hidden opacity-20 pointer-events-none -z-10">
          {Array.from({ length: 6 }).map((_, i) => (
            <Motion.div
              key={i}
              className="absolute rounded-full"
              style={{
                width: Math.random() * 300 + 100,
                height: Math.random() * 300 + 100,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                background: 'radial-gradient(circle, rgba(99,102,241,0.2) 0%, rgba(168,85,247,0) 70%)',
              }}
              animate={{
                x: [0, Math.random() * 60 - 30, 0],
                y: [0, Math.random() * 60 - 30, 0],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: Math.random() * 20 + 20,
                repeat: Infinity,
                repeatType: 'reverse' as const,
                ease: 'easeInOut',
              }}
            />
          ))}
        </div>
        
        {/* Logo/Brand */}
        <Motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mb-10 flex items-center justify-center"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full blur-xl opacity-30"></div>
            <div className="relative bg-gradient-to-br from-slate-900 to-slate-800 p-2 rounded-full">
              <Wand2 className="w-10 h-10 text-white" />
            </div>
          </div>
          <h1 className="ml-3 text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400">
            PxlMorph
          </h1>
          <style jsx global>{`
            @keyframes float {
              0% { transform: translateY(0); }
              50% { transform: translateY(-16px); }
              100% { transform: translateY(0); }
            }
            .animate-float {
              animation: float 3s ease-in-out infinite;
            }
            @keyframes gradient-move {
              0% { background-position: 0% 50%; }
              50% { background-position: 100% 50%; }
              100% { background-position: 0% 50%; }
            }
            .animate-gradient-move {
              background: linear-gradient(270deg, #7c3aed, #2563eb, #06b6d4, #7c3aed);
              background-size: 400% 400%;
              animation: gradient-move 6s ease-in-out infinite;
            }
          `}</style>
        </Motion.div>

        {/* Main Content */}
        <div className="max-w-2xl mx-auto">
          <div className="flex flex-col gap-3 items-center text-center">
            <Motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-4xl sm:text-5xl font-bold text-white mb-2 leading-tight"
            >
              Transform Ideas into{' '}
              <span className="gradient-text">
                Stunning AI Art
              </span>
            </Motion.h1>

            <Motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="text-lg text-slate-300 mb-2"
            >
              Create breathtaking visuals with AI-powered tools. No design skills needed.
            </Motion.p>

            <Motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="flex flex-col items-center justify-center mb-12"
            >
              <Button
                onClick={() => {
                  if (user) {
                    router.push("/studio");
                  } else {
                    router.push("/login");
                  }
                }}
                className="mx-auto flex items-center justify-center gap-4 px-12 py-7 min-w-[320px] min-h-[72px] text-2xl sm:text-3xl font-bold rounded-2xl transform-btn-animated text-white shadow-2xl transition-all duration-300 btn-glow hover-lift border-0"
                aria-label="Start Creating Free"
              >
                <Wand2 className="h-8 w-8 mr-4 -ml-2" />
                Start Creating Free
              </Button>
            </Motion.div>
          </div>
        </div>

        {/* Features */}
        <Motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-4xl mx-auto mt-10"
        >
          {features.map((feature, index) => (
            <Motion.div 
              key={index}
              className="bg-gradient-to-br from-white/5 to-white/3 backdrop-blur-sm p-5 rounded-2xl border border-white/5 hover:border-white/10 transition-all duration-300 flex flex-col items-center text-center"
              whileHover={{ y: -5, boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' }}
            >
              <div className="w-14 h-14 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-xl flex items-center justify-center text-2xl mb-3">
                {feature.icon}
              </div>
              <h3 className="text-lg font-semibold text-white mb-1">{feature.title}</h3>
              <p className="text-sm text-slate-400">{feature.description}</p>
            </Motion.div>
          ))}
        </Motion.div>
        
        {/* Footer */}
        <Motion.footer
          initial={{ opacity: 0, y: 24 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="text-center text-sm text-slate-400/80 mt-8"
        >
          <Motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.65 }}
          >
            Trusted by creators worldwide
          </Motion.p>
          <Motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="mt-2 flex justify-center space-x-4"
          >
             {['🎨', '📱', '🎭', '🖌️', '🎭'].map((emoji, i) => (
              <Motion.span
                key={i}
                initial={{ opacity: 0, y: 6 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.75 + i * 0.05 }}
                className="opacity-70 hover:opacity-100 transition-opacity"
              >
                 {emoji}
              </Motion.span>
             ))}
          </Motion.div>
        </Motion.footer>
      </div>
    </div>
  );
}
