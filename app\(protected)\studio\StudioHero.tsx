import { Wand2 } from "lucide-react";
import React from "react";

export function StudioHero() {
  return (
    <div className="flex justify-center">
      {/* Essential icon container only */}
      <div className="flex-shrink-0 p-0 w-10 h-10 sm:w-14 sm:h-14 lg:w-14 lg:h-14">
        <div className="p-2 sm:p-2 lg:p-2 bg-gradient-to-br from-violet-600 to-blue-500 rounded-full border-2 border-white/20 animate-gradient-move flex items-center justify-center w-full h-full">
          <Wand2 className="text-white drop-shadow-xl w-full h-full" />
        </div>
      </div>
      <div className="inline-block align-top">
        <h1 className="block w-auto text-2xl sm:text-4xl lg:text-4xl font-bold gradient-text leading-none">
          PxlMorph AI Studio
        </h1>
        <p className="block w-auto text-base sm:text-lg lg:text-base text-slate-300 leading-none">
          Transform any photo with the magic of AI-powered style transfer
        </p>
      </div>
    </div>
  );
}
