"use client";

import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import {
  Upload,
  X,
  Image as ImageIcon,
  Wand2,
  AlertCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { IMAGE_GENERATION_CONFIG } from "@/lib/constants";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipArrow,
} from "@/components/ui/tooltip";

interface ImageUploaderProps {
  onImageSelect: (file: File | null) => void;
  selectedImage: File | null;
  hasBeenUsed?: boolean;
}

export function ImageUploader({
  onImageSelect,
  selectedImage,
  hasBeenUsed = false,
}: ImageUploaderProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [objectUrl, setObjectUrl] = useState<string | null>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const [isFileDialogOpen, setIsFileDialogOpen] = useState(false);
  const lastDialogOpenTime = useRef(0);

  // Reset dialog open state when window regains focus (file dialog closes)
  useEffect(() => {
    const handleWindowFocus = () => {
      setIsFileDialogOpen(false);
    };
    window.addEventListener("focus", handleWindowFocus);
    return () => {
      window.removeEventListener("focus", handleWindowFocus);
    };
  }, []);

  useEffect(() => {
    if (selectedImage) {
      const url = URL.createObjectURL(selectedImage);
      setObjectUrl(url);
      setImageLoaded(false);
      return () => {
        URL.revokeObjectURL(url);
      };
    } else {
      setObjectUrl(null);
      setImageLoaded(false);
    }
  }, [selectedImage]);

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find((file) =>
      IMAGE_GENERATION_CONFIG.allowedFileTypes.includes(file.type),
    );
    if (imageFile && imageFile.size <= IMAGE_GENERATION_CONFIG.maxFileSize) {
      onImageSelect(imageFile);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('[ImageUploader] handleFileSelect called', e.target.files);
    setIsFileDialogOpen(false);
    const file = e.target.files?.[0];
    // Always reset the input value so the same file can be selected again
    if (fileInputRef.current) fileInputRef.current.value = "";
    if (file && file.size <= IMAGE_GENERATION_CONFIG.maxFileSize) {
      onImageSelect(file);
    }
  };

  const handleRemoveImage = () => {
    onImageSelect(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleOpenFileDialog = () => {
    const now = Date.now();
    // Debounce: prevent opening again within 500ms
    if (isFileDialogOpen && now - lastDialogOpenTime.current < 500) {
      console.log('[ImageUploader] File dialog already open or just opened, ignoring click');
      return;
    }
    setIsFileDialogOpen(true);
    lastDialogOpenTime.current = now;
    fileInputRef.current?.click();
  };

  if (selectedImage && objectUrl) {
    return (
      <div className="relative">
        <div
          ref={imageContainerRef}
          className="relative w-full aspect-video glass-card rounded-xl overflow-hidden"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <Image
            src={objectUrl}
            alt="Selected image for AI transformation"
            fill
            style={{ objectFit: "contain" }}
            className={`transition-opacity duration-500 ${imageLoaded ? "opacity-100" : "opacity-0"}`}
            onLoad={handleImageLoad}
          />
          <button
            type="button"
            className={`absolute top-3 left-3 z-10 bg-red-500/90 hover:bg-red-600 text-white rounded-full p-2 shadow-lg transition-all duration-200 focus-visible:ring-2 focus-visible:ring-red-400 outline-none hover:shadow-xl ${isHovered ? "opacity-100" : "opacity-0 pointer-events-none"}`}
            onClick={handleRemoveImage}
            aria-label="Remove selected image"
          >
            <X className="h-4 w-4" />
          </button>
          <button
            type="button"
            className={`absolute top-3 right-3 z-10 bg-slate-900/80 hover:bg-violet-600 text-white rounded-full p-2 border border-white/20 shadow-lg transition-all duration-200 focus-visible:ring-2 focus-visible:ring-violet-400 outline-none hover:shadow-xl hover:border-violet-400 ${isHovered ? "opacity-100" : "opacity-80"}`}
            onClick={(e) => {
              e.stopPropagation();
              handleOpenFileDialog();
            }}
            aria-label="Change picture"
          >
            <ImageIcon className="h-4 w-4" />
          </button>
          <div
            className="absolute bottom-8 left-1/2 -translate-x-1/2 z-20 flex items-center gap-1 px-3 py-1 rounded-full status-chip-animated animate-fadein"
            style={{
              minWidth: "max-content",
              fontWeight: 500,
              fontSize: "0.95rem",
              letterSpacing: "0.01em",
              borderRadius: "9999px",
              position: "absolute",
              left: "50%",
              bottom: "2rem",
              transform: "translateX(-50%)",
              overflow: "visible",
              background:
                "linear-gradient(90deg, #8b5cf6cc, #2563ebcc, #06b6d4cc, #8b5cf6cc)",
              backgroundSize: "400% 400%",
              animation: "gradientShift 3s ease-in-out infinite",
              border: "1px solid rgba(139,92,246,0.12)",
              boxShadow: "0 2px 8px 0 rgba(124,58,237,0.10)",
              backdropFilter: "blur(10px)",
              color: "white",
            }}
          >
            <Wand2 className="h-4 w-4 mr-1 text-white opacity-80" />
            <span className="font-medium text-xs text-white opacity-90">
              {hasBeenUsed
                ? "Ready for more transformations • Try different styles"
                : "Ready for AI transformation • Try different styles"}
            </span>
          </div>
        </div>
        <input
          ref={fileInputRef}
          type="file"
          accept={IMAGE_GENERATION_CONFIG.allowedFileTypes.join(",")}
          onChange={handleFileSelect}
          className="hidden"
          name="image-upload"
        />
        <p className="text-xs text-slate-500 mt-1 text-center">
          {/* Removed output text */}
        </p>
        <style jsx global>{`
          @keyframes fadein {
            from {
              opacity: 0;
              transform: translateY(16px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
          .animate-fadein {
            animation: fadein 0.7s cubic-bezier(0.4, 1, 0.6, 1);
          }
          .goat-badge-animated {
            background: linear-gradient(
              90deg,
              #8b5cf6,
              #2563eb,
              #06b6d4,
              #8b5cf6
            );
            background-size: 400% 400%;
            animation: gradientShift 3s ease-in-out infinite;
            border: 1.5px solid rgba(139, 92, 246, 0.18);
            box-shadow:
              0 4px 32px 0 rgba(124, 58, 237, 0.18),
              0 2px 12px 0 rgba(30, 41, 59, 0.1) inset;
            backdrop-filter: blur(18px);
            position: relative;
            overflow: visible;
          }
          @keyframes gradientShift {
            0%,
            100% {
              background-position: 0% 50%;
            }
            50% {
              background-position: 100% 50%;
            }
          }
          .goat-badge-glow {
            z-index: 0;
            background: radial-gradient(
              ellipse at center,
              rgba(139, 92, 246, 0.18) 0%,
              rgba(59, 130, 246, 0.1) 60%,
              transparent 100%
            );
            filter: blur(10px) saturate(1.4);
            opacity: 0.85;
            transition: opacity 0.3s;
            pointer-events: none;
          }
        `}</style>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 cursor-pointer group",
        isDragOver
          ? "border-violet-500 bg-violet-500/10"
          : "border-slate-600 hover:border-violet-400 hover:bg-violet-500/5",
      )}
      onClick={handleOpenFileDialog}
      tabIndex={0}
      role="button"
      aria-label="Upload image"
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <div className="flex flex-col items-center space-y-4">
        <div
          className={cn(
            "relative p-6 rounded-full transition-all duration-300",
            isDragOver
              ? "bg-gradient-to-r from-violet-500 to-blue-500"
              : "bg-slate-800/50 group-hover:bg-slate-700/50",
          )}
        >
          {isDragOver && (
            <div className="absolute inset-0 bg-gradient-to-r from-violet-500 to-blue-500 rounded-full blur-xl opacity-50"></div>
          )}
          <Wand2 className={cn(
            "h-8 w-8 relative z-10 transition-colors duration-300",
            isDragOver ? "text-white animate-bounce" : "text-slate-400 group-hover:text-slate-300"
          )} />
        </div>

        <div className="space-y-3">
          <div className="space-y-1">
            <p
              className={cn(
                "text-lg font-semibold transition-colors duration-300",
                isDragOver
                  ? "text-violet-300"
                  : "text-slate-200 group-hover:text-white",
              )}
            >
              {isDragOver ? "Drop your image here" : "Upload Your Image"}
            </p>
            <p className="text-sm text-slate-400 group-hover:text-slate-300 transition-colors duration-300">
              Drag & drop or click to select an image to use for your artwork
            </p>
          </div>

          <div className="text-xs text-slate-500 space-y-1">
            <p>Supported: PNG, JPG, GIF</p>
            <p>Maximum size: 4MB</p>
          </div>
        </div>

        <Button
          onClick={handleOpenFileDialog}
          className="mx-auto flex items-center gap-2 px-6 py-3 min-w-[240px] rounded-xl bg-gradient-to-r from-violet-500 to-purple-500 hover:from-violet-600 hover:to-purple-600 border-0 text-white font-semibold text-base shadow-xl transition-all duration-300 btn-glow hover-lift"
          aria-label="Select Image to Transform"
        >
          <Upload className="h-5 w-5 mr-2 -ml-1" />
          Select Image to Transform
        </Button>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept={IMAGE_GENERATION_CONFIG.allowedFileTypes.join(",")}
        onChange={handleFileSelect}
        className="hidden"
        name="image-upload"
      />
    </div>
  );
}
