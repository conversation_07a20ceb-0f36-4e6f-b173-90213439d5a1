"use client";

import { useState, useTransition } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Wand2 } from "lucide-react";
import { toast } from "sonner";
import { useAnalytics } from "@/lib/hooks/useAnalytics";
import { supabase } from "@/lib/supabase";

interface AuthFormProps {
  onAuthSuccess: () => void;
  onFadingOutDone?: () => void;
}

export function AuthForm({ onAuthSuccess, onFadingOutDone }: AuthFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [activeTab, setActiveTab] = useState<"signin" | "signup">("signin");
  const { trackUserLogin, trackUserSignup, trackError } = useAnalytics();
  const [isPending, startTransition] = useTransition();
  const [success, setSuccess] = useState("");
  const [isFadingOut, setIsFadingOut] = useState(false);

  async function handleAuth(isSignUp: boolean, email: string, password: string) {
    setIsLoading(true);
    setError("");
    setSuccess("");
    try {
      console.log("[DEBUG] handleAuth called", { isSignUp, email, passwordLength: password.length });
      let result;
      if (isSignUp) {
        result = await supabase.auth.signUp({ email, password });
      } else {
        result = await supabase.auth.signInWithPassword({ email, password });
      }
      console.log("[DEBUG] Supabase auth result", result);
      if (result.error) {
        setError(result.error.message);
        trackError("auth_error", result.error.message, isSignUp ? "signup" : "login");
        setIsLoading(false);
        return;
      }
      if (isSignUp) {
        setSuccess("Check your email to confirm your account.");
        trackUserSignup("email");
        setIsLoading(false);
      } else {
        trackUserLogin("email");
        // Set flag for StudioPage to show welcome toast
        if (typeof window !== "undefined") {
          window.sessionStorage.setItem("showWelcomeToast", "1");
        }
        setIsFadingOut(true);
        // Wait for fade-out animation, then call onFadingOutDone
        setTimeout(() => {
          setIsLoading(false);
          if (onFadingOutDone) onFadingOutDone();
          window.location.href = "/studio";
        }, 500); // match fade-out duration
      }
    } catch (err) {
      console.error("[DEBUG] Unexpected error in handleAuth", err);
      const errorMessage = "An unexpected error occurred";
      setError(errorMessage);
      trackError("auth_unexpected", errorMessage, isSignUp ? "signup" : "login");
      setIsLoading(false);
    }
  }

  return (
    <div className={`w-full ${isFadingOut ? 'animate-fadeOut' : ''}`}>
      <div className="w-full max-w-md page-transition">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-violet-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl"></div>
        </div>

        <Card className="glass-card card-entrance">
          <CardHeader className="text-center space-y-4 card-entrance">
            <div className="flex justify-center">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-violet-500 to-blue-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
                <div className="relative p-4 bg-gradient-to-r from-violet-500 to-blue-500 rounded-full">
                  <Wand2 className="h-8 w-8 text-white" />
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <CardTitle className="text-4xl font-extrabold gradient-text drop-shadow-lg leading-tight pb-1">
                PxlMorph AI
              </CardTitle>
              <CardDescription className="text-lg text-blue-200/90 font-medium drop-shadow-sm">
                Unleash your creativity with AI-powered art
              </CardDescription>
            </div>
            <div className="flex items-center justify-center gap-2 text-base text-teal-300/80 font-semibold">
              <span className="text-slate-400/80 font-light text-xs">
                Stunning visuals, effortless creation
              </span>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs
              defaultValue="signin"
              className="w-full"
              onValueChange={(v) => setActiveTab(v as "signin" | "signup")}
            >
              <TabsList className="relative w-full grid grid-cols-2 bg-slate-800/50 rounded-xl overflow-hidden">
                {/* Animated sliding pill highlight */}
                <span
                  className={`absolute top-1 left-1 h-[calc(100%-0.5rem)] w-[calc(50%-0.25rem)] rounded-xl bg-gradient-to-r from-violet-500/60 to-blue-500/60 bg-opacity-60 backdrop-blur-md transition-all duration-300 z-10 ${activeTab === "signup" ? "translate-x-full" : ""}`}
                  aria-hidden="true"
                />
                <TabsTrigger
                  value="signin"
                  className="relative z-20 font-semibold bg-transparent transition-colors cursor-pointer data-[state=active]:bg-transparent data-[state=active]:text-white data-[state=inactive]:text-slate-300 hover:text-white hover:bg-slate-700/40"
                  onClick={() => setActiveTab("signin")}
                  data-testid="tab-signin"
                >
                  Sign In
                </TabsTrigger>
                <TabsTrigger
                  value="signup"
                  className="relative z-20 font-semibold bg-transparent transition-colors cursor-pointer data-[state=active]:bg-transparent data-[state=active]:text-white data-[state=inactive]:text-slate-300 hover:text-white hover:bg-slate-700/40"
                  onClick={() => setActiveTab("signup")}
                  data-testid="tab-signup"
                >
                  Sign Up
                </TabsTrigger>
              </TabsList>

              <TabsContent value="signin" className="space-y-4 mt-6">
                <AuthTabContent
                  title="Welcome back, creator"
                  buttonText="Sign In"
                  onSubmit={(email, password) => {
                    startTransition(() => handleAuth(false, email, password));
                  }}
                  isLoading={isLoading || isPending}
                  testId="btn-signin"
                />
              </TabsContent>

              <TabsContent value="signup" className="space-y-4 mt-6">
                <AuthTabContent
                  title="Begin your artistic journey"
                  buttonText="Create Account"
                  onSubmit={(email, password) => {
                    startTransition(() => handleAuth(true, email, password));
                  }}
                  isLoading={isLoading || isPending}
                  testId="btn-signup"
                />
              </TabsContent>
            </Tabs>

            {error && (
              <Alert
                className="mt-4 border-red-500/30 bg-red-500/10"
                variant="destructive"
                data-testid="auth-error"
              >
                <AlertDescription className="text-red-200">
                  {error}
                </AlertDescription>
              </Alert>
            )}
            {success && (
              <Alert className="mt-4 border-green-500/30 bg-green-500/10" variant="default" data-testid="auth-success">
                <AlertDescription className="text-green-200">{success}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

interface AuthTabContentProps {
  title: string;
  buttonText: string;
  onSubmit: (email: string, password: string) => void;
  isLoading: boolean;
  testId: string;
}

function AuthTabContent({
  title,
  buttonText,
  onSubmit,
  isLoading,
  testId,
}: AuthTabContentProps) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(email, password);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-medium text-slate-200">{title}</h3>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email" className="text-slate-300">
            Email
          </Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="bg-slate-800/50 border-slate-700 focus:border-violet-500 text-white placeholder:text-slate-500"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="password" className="text-slate-300">
            Password
          </Label>
          <Input
            id="password"
            type="password"
            placeholder="Enter your password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="bg-slate-800/50 border-slate-700 focus:border-violet-500 text-white placeholder:text-slate-500"
            required
          />
        </div>
      </div>

      <Button
        type="submit"
        className="w-full bg-gradient-to-r from-violet-500 to-blue-500 hover:from-violet-600 hover:to-blue-600 text-white py-6 rounded-xl btn-glow hover-lift"
        disabled={isLoading}
        data-testid={testId}
      >
        {isLoading ? (
          <span className="mr-2 inline-block h-5 w-5 border-2 border-white border-t-transparent rounded-full animate-spin align-middle" />
        ) : null}
        {buttonText}
      </Button>
    </form>
  );
}
