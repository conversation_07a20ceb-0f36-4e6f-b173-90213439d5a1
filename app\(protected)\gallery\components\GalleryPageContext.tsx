import React, { createContext, useContext, useMemo } from "react";
import { useGalleryLogic } from "./useGalleryLogic";

// Infer the type from useGalleryLogic
export type GalleryPageContextType = ReturnType<typeof useGalleryLogic>;

const GalleryPageContext = createContext<GalleryPageContextType | null>(null);

export function GalleryPageProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const logic = useGalleryLogic();
  
  // Properly memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => logic, [
    logic.images,
    logic.filteredImages,
    logic.isLoading,
    logic.error,
    logic.searchQuery,
    logic.styleFilter,
    logic.shareFilter,
    logic.viewMode,
    logic.hasMore,
    logic.styles,
    logic.handleImageShare,
    logic.handleImageDelete,
    logic.handleSearchChange,
    logic.handleStyleFilterChange,
    logic.handleShareFilterChange,
    logic.loadMore,
  ]);
  
  return (
    <GalleryPageContext.Provider value={value}>
      {children}
    </GalleryPageContext.Provider>
  );
}

export function useGalleryPageContext() {
  const ctx = useContext(GalleryPageContext);
  if (!ctx)
    throw new Error(
      "useGalleryPageContext must be used within a GalleryPageProvider",
    );
  return ctx;
}
