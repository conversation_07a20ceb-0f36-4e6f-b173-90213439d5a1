
"use client";

import React, { useState, useMemo, useCallback, useEffect, Suspense, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { <PERSON>rk<PERSON>, Star, StarOff } from "lucide-react";
import { useStyles, useStylesByCategory } from "@/lib/contexts/StylesContext";
import { useUser, useIsFavorite } from "@/lib/contexts/UserContext";
import { useDebounce } from "@/hooks/use-debounce";
import { OptimizedImage } from "@/components/ui/optimized-image";
import { Style } from "@/lib/types";
import dynamic from 'next/dynamic';
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";
import {
  useRecentlyUsedStyles,
  RecentlyUsedStyle,
} from "@/lib/contexts/RecentlyUsedStylesContext";
import { supabase } from "@/lib/supabase";
import { Skeleton } from "@/components/ui/skeleton";
import useS<PERSON> from "swr";
import { Input } from "@/components/ui/input";

const fetcher = (url: string) => fetch(url).then((res) => res.json());

// Helper function for image per style
const sharedImage =
  "https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80";

type CategoryOrAll = string;

interface StyleSelectorProps {
  selectedStyle: string;
  onStyleSelect: (styleId: string) => void;
}

const ALL_TAB = "All";

// Helper to get the correct image source, preferring the optimized version
const getOptimizedImageSrc = (
  style: Style,
  mapping: Record<string, string> | undefined
): string => {
  if (mapping && mapping[style.id]) {
    return `/${mapping[style.id]}`;
  }
  if (style.example_image && style.example_image.trim() !== "") {
    return style.example_image;
  }
  return sharedImage;
};

export function StyleSelector({
  selectedStyle,
  onStyleSelect,
}: StyleSelectorProps) {
  const [selectedCategory, setSelectedCategory] = useState<
    CategoryOrAll | "Favorites" | "Recently Used"
  >(ALL_TAB);
  const [searchQuery, setSearchQuery] = useState("");
  const { user, favorites, favoritesLoading, refreshFavorites } = useUser();
  const gridRef = useRef<HTMLDivElement>(null);
  const scrollAnchorRef = useRef<HTMLDivElement>(null);
  const prevImageCount = useRef(0);

  // Fetch the optimized image mapping
  const { data: optimizedMapping } = useSWR<Record<string, string>>(
    "/optimized-style-mapping.json",
    fetcher
  );

  const [pendingFavorite, setPendingFavorite] = useState<{
    styleId: string;
    action: "add" | "remove";
  } | null>(null);

  const [optimisticFavorites, setOptimisticFavorites] = useState<string[]>(favorites);
  useEffect(() => {
    setOptimisticFavorites(favorites);
  }, [favorites]);

  React.useEffect(() => {
    if (!pendingFavorite) return;
    const { styleId, action } = pendingFavorite;
    const isNowFavorite = favorites.includes(styleId);
    if (
      (action === "add" && isNowFavorite) ||
      (action === "remove" && !isNowFavorite)
    ) {
      setPendingFavorite(null);
    }
  }, [favorites, pendingFavorite]);

  const {
    styles: recentlyUsedStyles,
    loaded: recentlyLoaded,
  } = useRecentlyUsedStyles();

  const { styles, isLoading: stylesLoading, error: stylesError } = useStyles();
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const categoryMapping = useMemo(() => {
    const mapping: Record<string, string[]> = {};
    styles.forEach((style) => {
      if (style.category && !mapping[style.category]) {
        mapping[style.category] = [];
      }
      if (style.category) {
        mapping[style.category].push(style.id);
      }
    });
    return mapping;
  }, [styles]);

  const unifiedTabs = useMemo(() => {
    const tabs = [
      { key: ALL_TAB, label: ALL_TAB },
    ];
    if (favorites.length > 0) {
      tabs.push({ key: "Favorites", label: `Favorites (${favorites.length})` });
    }
    if (recentlyLoaded && recentlyUsedStyles.length > 0) {
      tabs.push({ key: "Recently Used", label: `Recently Used (${recentlyUsedStyles.length})` });
    }
    Object.keys(categoryMapping).sort().forEach((cat) => {
      tabs.push({ key: cat, label: cat });
    });
    return tabs;
  }, [favorites.length, recentlyLoaded, recentlyUsedStyles.length, categoryMapping]);

  const filteredStyles = useMemo(() => {
    let sourceStyles: (Style | RecentlyUsedStyle)[] = [];
    if (selectedCategory === "Recently Used") {
      sourceStyles = recentlyUsedStyles;
    } else if (selectedCategory === "Favorites") {
      sourceStyles = styles.filter(s => optimisticFavorites.includes(s.id));
    } else if (selectedCategory === ALL_TAB) {
      sourceStyles = styles;
    } else {
      sourceStyles = styles.filter(s => s.category === selectedCategory);
    }
    let filtered = sourceStyles.filter(s => {
      if (!s || !s.id || !s.name) return false;
      if (selectedCategory === "Recently Used") {
        return 'createdAt' in s;
      }
      return true;
    });

    if (debouncedSearchQuery) {
      filtered = filtered.filter(s =>
        s.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase())
      );
    }
    return filtered.filter(Boolean);
  }, [selectedCategory, debouncedSearchQuery, optimisticFavorites, recentlyUsedStyles, styles]);

  const handleFilterChange = useCallback(() => {
    if (prevImageCount.current > 0 && filteredStyles.length > prevImageCount.current) {
      scrollAnchorRef.current?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
    prevImageCount.current = filteredStyles.length;
  }, [filteredStyles]);

  useEffect(() => {
    handleFilterChange();
  }, [filteredStyles, handleFilterChange]);
  
  const handleToggleFavorite = useCallback(
    async (styleId: string) => {
      if (!user || pendingFavorite) return;
      const isCurrentlyFavorite = optimisticFavorites.includes(styleId);
      const action = isCurrentlyFavorite ? "remove" : "add";
      setPendingFavorite({ styleId, action });
      setOptimisticFavorites((prev) =>
        action === "add"
          ? [...prev, styleId]
          : prev.filter((id) => id !== styleId)
      );
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        if (!session) {
          throw new Error("No session found");
        }
        const response = await fetch("/api/user/favorites", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ styleId, action }),
        });
        if (response.ok) {
          refreshFavorites();
          const style = styles.find((s) => s.id === styleId);
          if (style) {
            toast(
              action === "add"
                ? `"${style.name}" has been added to your favorites.`
                : `"${style.name}" has been removed from your favorites.`
            );
          }
        } else {
          throw new Error("Failed to toggle favorite");
        }
      } catch (error) {
        setOptimisticFavorites(favorites);
        setPendingFavorite(null);
        alert("Failed to update favorites. Please try again.");
      }
    },
    [user, optimisticFavorites, refreshFavorites, pendingFavorite, styles, favorites],
  );

  if (stylesLoading) {
    return (
      <div className="space-y-4">
        <div className="text-center py-16">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading styles...</p>
        </div>
      </div>
    );
  }

  if (stylesError) {
    return (
      <div className="space-y-4">
        <div className="text-center py-16">
          <div className="text-destructive text-2xl mb-2">⚠️</div>
          <p className="text-muted-foreground">Failed to load styles</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const focusRing =
    "focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2";

  return (
    <div className="space-y-2 z-0">
      <div className="flex flex-col sm:flex-row gap-2 items-center mb-2">
        <div className="relative w-full sm:w-auto sm:flex-1">
          <Input
            type="text"
            placeholder="Search styles..."
            value={searchQuery}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
            className="h-9 w-full pl-4 pr-10"
          />
          <Sparkles className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
        </div>
      </div>

      <div className={cn("flex flex-wrap items-center justify-center gap-2 px-1 mb-2")}>
        {unifiedTabs.map((tab) => (
          <button
            key={tab.key}
            onClick={() => setSelectedCategory(tab.key)}
            className={cn(
              "select-text cursor-pointer",
              "px-3 py-1.5 h-9 rounded-lg text-xs font-semibold transition-all duration-300 ease-out",
              "hover:bg-accent hover:text-accent-foreground",
              focusRing,
              selectedCategory === tab.key
                ? "bg-primary text-primary-foreground shadow-[0_0_12px_-2px_hsl(var(--primary)_/_0.4)]"
                : "bg-secondary text-muted-foreground border border-transparent",
            )}
          >
            {tab.key === "Favorites" && <Star className="w-3 h-3 inline mr-1.5" />}
            {tab.key}
          </button>
        ))}
      </div>

      <div ref={gridRef} className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
        <AnimatePresence mode="popLayout">
          {filteredStyles.map((style, index) => {
            const isFavoriteRaw = optimisticFavorites.includes(style.id);
            let isFavorite = isFavoriteRaw;
            if (pendingFavorite && pendingFavorite.styleId === style.id) {
              isFavorite = pendingFavorite.action === "add";
            }
            const isSelected = selectedStyle === style.id;
            const imageSrc = getOptimizedImageSrc(style as Style, optimizedMapping);

            return (
              <motion.div
                key={style.id}
                layout
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 40,
                }}
                className="w-full min-w-0"
              >
                <div
                  className={cn(
                    "relative group style-card-hover cursor-pointer w-full min-w-0 will-change-transform",
                    isSelected && "style-card-selected",
                    focusRing,
                  )}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "flex-start",
                  }}
                  onClick={() => onStyleSelect(style.id)}
                  tabIndex={0}
                  role="button"
                  aria-label={`Select style ${style.name}`}
                >
                  <Card className="bg-card w-full min-w-0 relative z-0">
                    <CardContent className="p-0 flex-1 flex flex-col items-center justify-start">
                      <div className="style-card-image-container aspect-square flex items-center justify-center overflow-hidden bg-transparent relative">
                        <OptimizedImage
                          src={imageSrc}
                          alt=""
                          width={200}
                          height={200}
                          fallbackSrc={sharedImage}
                          className="w-full h-full object-cover"
                          quality={75}
                          priority={index < 8}
                          loading={index < 8 ? "eager" : "lazy"}
                          fetchPriority={index < 8 ? "high" : "auto"}
                          onError={() => {
                            if (process.env.NODE_ENV === 'development') {
                              console.warn('Image failed to load:', imageSrc);
                            }
                          }}
                        />
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleToggleFavorite(style.id);
                          }}
                          disabled={
                            !!favoritesLoading ||
                            !!(
                              pendingFavorite &&
                              pendingFavorite.styleId === style.id
                            )
                          }
                          aria-label={isFavorite ? `Remove ${style.name} from favorites` : `Add ${style.name} to favorites`}
                          className={cn(
                            "absolute top-2 right-2 p-1.5 rounded-full transition-all duration-200",
                            "hover:bg-black/50 hover:scale-110",
                            focusRing,
                            isFavorite
                              ? "text-yellow-400 bg-yellow-400/20"
                              : "text-muted-foreground bg-black/50",
                          )}
                        >
                          {isFavorite ? (
                            <Star className="w-4 h-4" />
                          ) : (
                            <StarOff className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    </CardContent>
                    <div className="space-y-1 mb-1 mt-1 px-2">
                      <h3 className="font-medium text-foreground text-sm line-clamp-2 text-center">
                        {style.name}
                      </h3>
                    </div>
                  </Card>
                </div>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>
      <div ref={scrollAnchorRef} />
    </div>
  );
}

export const MemoizedStyleSelector = React.memo(StyleSelector);
