import React from "react";
import { cn } from "@/lib/utils";

interface UnifiedLoaderProps {
  text?: string;
  className?: string;
}

const UnifiedLoader: React.FC<UnifiedLoaderProps> = ({
  text = "Loading...",
  className = "",
}) => (
  <div
    className={cn(
      "flex flex-col items-center justify-center w-full h-full py-8",
      className
    )}
    role="status"
    aria-live="polite"
  >
    <div
      className="relative h-12 w-12 mb-3"
      style={{ filter: "drop-shadow(0 0 24px rgba(16, 185, 129, 0.4))" }}
    >
      <div className="absolute inset-0 rounded-full animate-spinner-spin bg-spinner-gradient"></div>
      <div className="absolute inset-1 rounded-full bg-background"></div>
    </div>

    <span className="text-base text-muted-foreground font-medium select-none drop-shadow-lg">
      {text}
    </span>
  </div>
);

export default UnifiedLoader;
