import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>, Feather, LayoutGrid, Rocket, Wand2 } from "lucide-react";
import Link from "next/link";
import { ThemeToggle } from "@/components/theme-toggle";

export default function Home() {
  return (
    <div className="flex flex-col min-h-dvh bg-background text-foreground">
      <header className="border-b sticky top-0 bg-background/95 backdrop-blur z-10">
        <div className="container mx-auto flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-2">
            <Wand2 className="h-6 w-6 text-primary" />
            <h1 className="text-xl font-bold font-headline">PxlMorph AI</h1>
          </div>
          <ThemeToggle />
        </div>
      </header>
      <main className="flex-1">
        <section className="container mx-auto px-4 py-16 md:py-24 text-center">
          <h2 className="text-4xl md:text-5xl font-bold font-headline tracking-tight">Your Next Project Starts Here</h2>
          <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
            This is a blank starter project. Clean, modern, and ready for you to build upon.
          </p>
          <div className="mt-8">
            <Button asChild size="lg">
              <Link href="/studio">Get Started</Link>
            </Button>
          </div>
        </section>
        <section className="bg-secondary py-16 md:py-24">
          <div className="container mx-auto px-4">
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader>
                  <LayoutGrid className="h-8 w-8 text-primary mb-2" />
                  <CardTitle>Clean Layout</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>A clean, modern layout to get users started quickly.</CardDescription>
                </CardContent>
              </Card>
              <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader>
                  <Code className="h-8 w-8 text-primary mb-2" />
                  <CardTitle>Essential Configs</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>Includes necessary configuration for basic functionality.</CardDescription>
                </CardContent>
              </Card>
              <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader>
                  <Rocket className="h-8 w-8 text-primary mb-2" />
                  <CardTitle>Sample Pages</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>Example components and pages that can be easily understood.</CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
      <footer className="border-t">
          <div className="container mx-auto flex h-16 items-center justify-center px-4">
            <p className="text-sm text-muted-foreground">
              Built with Next.js and ShadCN/UI.
            </p>
          </div>
      </footer>
    </div>
  );
}
