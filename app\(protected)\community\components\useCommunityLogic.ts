import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { useCommunityImages } from "@/lib/hooks/useCommunityImages";
import { useStyles } from "@/lib/contexts/StylesContext";
import { useUser } from "@/lib/contexts/UserContext";
import { GeneratedImage } from "@/lib/types";
import { useImageSharing } from "@/lib/hooks/useImageSharing";
import { toast } from "sonner";

export type SortByType = "recent" | "trending";

// Helper to check if two arrays of images are different (by length or first/last ID)
function imagesChanged(a?: GeneratedImage[], b?: GeneratedImage[]) {
  if (!a || !b) return true;
  if (a.length !== b.length) return true;
  if (a.length === 0) return false;
  return a[0].id !== b[0].id || a[a.length - 1].id !== b[b.length - 1].id;
}

export function useCommunityLogic() {
  const [sortOrder, _setSortOrder] = useState<SortByType>("recent");
  const [page, setPage] = useState<{ [key in SortByType]: number }>({ recent: 0, trending: 0 });
  const [cache, setCache] = useState<{ [key in SortByType]: GeneratedImage[] }>({ recent: [], trending: [] });
  const [loading, setLoading] = useState<{ [key in SortByType]: boolean }>({ recent: false, trending: false });
  const IMAGES_PER_PAGE = 20;
  // Track previous image IDs for deduplication
  const prevImageIds = useRef<{ [key in SortByType]: Set<string> }>({ recent: new Set(), trending: new Set() });
  // Track last sortOrder to detect real changes
  const lastSortOrder = useRef<SortByType>(sortOrder);

  // Custom setSortOrder that resets page/cache/dedup set for new sortOrder
  const setSortOrder = useCallback((newOrder: SortByType) => {
    setPage((p) => ({ ...p, [newOrder]: 0 }));
    setCache((c) => ({ ...c, [newOrder]: [] }));
    prevImageIds.current[newOrder] = new Set();
    _setSortOrder(newOrder);
  }, []);

  // Fetch current page for current sort order
  const { images, isLoading, error, hasMore, mutate } = useCommunityImages(
    sortOrder,
    IMAGES_PER_PAGE,
    page[sortOrder] * IMAGES_PER_PAGE
  );

  const { styles: rawStyles } = useStyles();
  const { user } = useUser();
  const styles = rawStyles || [];
  const [searchQuery, setSearchQuery] = useState("");
  const [styleFilter, setStyleFilter] = useState("all");
  const [clientIsOnline, setClientIsOnline] = useState(true);
  const preloadProgress = useState(0)[0];
  const preloadTimeout = useRef<NodeJS.Timeout | null>(null);
  const { handleImageShare: baseHandleImageShare } = useImageSharing();

  useEffect(() => {
    if (images && images.length > 0) {
      const prevIds = prevImageIds.current[sortOrder];
      setCache((c) => {
        if (sortOrder === "trending") {
          // For trending, always replace cache with latest backend order
          images.forEach(img => prevIds.add(img.id));
          return { ...c, trending: images };
        } else {
          // For recent, keep append/dedup logic for infinite scroll
          const existing = c[sortOrder] || [];
          const existingIds = new Set(existing.map((img) => img.id));
          // Only add new images that are not already in the cache
          const filteredNewImages = images.filter(img => !existingIds.has(img.id));
          filteredNewImages.forEach(img => prevIds.add(img.id));
          return { ...c, [sortOrder]: [...existing, ...filteredNewImages] };
        }
      });
    }
    setLoading((l) => {
      if (l[sortOrder] === isLoading) return l;
      return { ...l, [sortOrder]: isLoading };
    });
  }, [images, isLoading, sortOrder]);

  // Load more images (next page)
  const loadMore = useCallback(() => {
    if (isLoading || !hasMore) return;
    setPage((p) => ({ ...p, [sortOrder]: p[sortOrder] + 1 }));
  }, [sortOrder, isLoading, hasMore]);

  // Memoize the current sort order's images
  const allImages = useMemo(() => {
    let images = cache[sortOrder] || [];
    if (sortOrder === "trending") {
      images = [...images].sort((a, b) => {
        // Primary: like_count DESC
        if ((b.like_count || 0) !== (a.like_count || 0)) {
          return (b.like_count || 0) - (a.like_count || 0);
        }
        // Secondary: shared_at DESC
        const aShared = a.shared_at ? new Date(a.shared_at).getTime() : 0;
        const bShared = b.shared_at ? new Date(b.shared_at).getTime() : 0;
        if (bShared !== aShared) {
          return bShared - aShared;
        }
        // Tertiary: created_at DESC
        const aCreated = a.created_at ? new Date(a.created_at).getTime() : 0;
        const bCreated = b.created_at ? new Date(b.created_at).getTime() : 0;
        return bCreated - aCreated;
      });
    }
    return images;
  }, [cache, sortOrder]);

  // Memoize filtered images for better performance
  const filteredImages = useMemo(() => {
    let filtered = Array.isArray(allImages) ? [...allImages] : [];
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (image) =>
          image.style.toLowerCase().includes(query),
      );
    }
    if (styleFilter !== "all") {
      filtered = filtered.filter((image) => image.style === styleFilter);
    }
    return filtered;
  }, [allImages, searchQuery, styleFilter]);

  // Helper function to check if current user owns an image
  const isImageOwner: (image: GeneratedImage) => boolean = useCallback(
    (image: GeneratedImage): boolean => {
      return Boolean(user?.id && image.user_id === user.id);
    },
    [user?.id],
  );

  useEffect(() => {
    setClientIsOnline(typeof window !== "undefined" ? navigator.onLine : true);
    const handleOnlineStatusChange = () =>
      setClientIsOnline(
        typeof window !== "undefined" ? navigator.onLine : true,
      );
    window.addEventListener("online", handleOnlineStatusChange);
    window.addEventListener("offline", handleOnlineStatusChange);
    const handleImageShareStatusChanged = (_event: CustomEvent) => {
      mutate();
    };
    window.addEventListener(
      "imageShareStatusChanged",
      handleImageShareStatusChanged as EventListener,
    );
    return () => {
      window.removeEventListener("online", handleOnlineStatusChange);
      window.removeEventListener("offline", handleOnlineStatusChange);
      window.removeEventListener(
        "imageShareStatusChanged",
        handleImageShareStatusChanged as EventListener,
      );
    };
  }, [mutate]);

  // Enhanced handleImageShare that also triggers refresh
  const handleImageShare = useCallback(
    async (imageId: string, isShared: boolean) => {
      const success = await baseHandleImageShare(imageId, isShared);
      if (success) {
        if (!isShared && user?.id) {
          // Immediately filter out the unshared image from the cache for the current sortOrder
          setCache((prev) => {
            const currentCache = prev[sortOrder] || [];
            const updatedCache = currentCache.filter((img) => img.id !== imageId);
            return {
              ...prev,
              [sortOrder]: updatedCache,
            };
          });
          toast.info("Image unshared and removed from view.");
        } else {
          mutate();
        }
      }
    },
    [baseHandleImageShare, mutate, sortOrder, user?.id],
  );

  return {
    images: allImages,
    filteredImages,
    isLoading: loading[sortOrder] ?? false,
    error,
    searchQuery,
    setSearchQuery,
    styleFilter,
    setStyleFilter,
    clientIsOnline,
    refresh: mutate,
    styles,
    preloadProgress,
    sortOrder,
    setSortOrder,
    handleImageShare,
    isImageOwner,
    user,
    loadMore,
    hasMore,
  };
}
