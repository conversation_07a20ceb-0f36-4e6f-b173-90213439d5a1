"use client";
export const dynamic = "force-dynamic";
// Route: /profile
// Profile Page (protected)
// Shows user profile and stats. Requires authentication.
import ProfileClient from './ProfileClient';
import ProtectedRoute from "@/components/auth/ProtectedRoute";

export default function ProfilePage() {
  return (
    <ProtectedRoute>
      <main className="min-h-screen flex items-center justify-center w-full" aria-label="Profile main content">
        <div className="w-full flex justify-center">
          <ProfileClient />
        </div>
      </main>
    </ProtectedRoute>
  );
}
