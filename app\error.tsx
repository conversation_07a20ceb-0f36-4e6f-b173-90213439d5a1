"use client";

export default function GlobalError({ error, reset }: { error: Error; reset: () => void }) {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-slate-900 text-white">
      <h1 className="text-3xl font-bold mb-4">Something went wrong</h1>
      <p className="mb-4">{error?.message || "An unknown error occurred."}</p>
      <button
        className="px-4 py-2 bg-blue-600 rounded hover:bg-blue-700"
        onClick={() => reset()}
      >
        Try Again
      </button>
    </div>
  );
} 