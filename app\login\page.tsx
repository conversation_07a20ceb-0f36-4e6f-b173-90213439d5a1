// Route: /login
// Login & Signup Page (public)
// This page provides both sign in and sign up forms (tabbed interface).
// No authentication required. Redirects to /studio if already logged in.
"use client";
export const dynamic = "force-dynamic";
import { Suspense, useEffect, useState, useRef } from "react";
import { AuthForm } from "@/components/auth/auth-form";
import { SupabaseService } from "@/lib/supabase";
import { useSearchParams } from "next/navigation";
import { AlertTriangle } from "lucide-react";
import UnifiedLoader from "@/components/ui/unified-loader";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import AppBackground from "@/components/layout/BackgroundWrapper";

function LoginContent() {
  const searchParams = useSearchParams();
  const reason = searchParams?.get("reason");
  const signout = searchParams?.get("signout");
  const [isFadingOut, setIsFadingOut] = useState(false);
  const [shouldRedirect, setShouldRedirect] = useState(false);
  const toastShown = useRef(false);
  const router = useRouter();

  useEffect(() => {
    // Persistent signout toast
    if (typeof window !== "undefined" && window.sessionStorage.getItem("showSignoutToast") === "1") {
      toast.success("Signed out successfully.");
      window.sessionStorage.removeItem("showSignoutToast");
    }
  }, []);

  useEffect(() => {
    // Normal user check
    SupabaseService.getCurrentUser().then((user) => {
      if (user && user.email) {
        router.push("/studio");
      }
    });
  }, [router]);

  useEffect(() => {
    if (shouldRedirect) {
      router.push("/studio");
    }
  }, [shouldRedirect, router]);

  // Show toast if session expired
  useEffect(() => {
    if (reason === "session-expired" && !signout && !toastShown.current) {
      toast.error("Your session has expired. Please log in again.", {
        icon: <AlertTriangle className="h-6 w-6 text-white drop-shadow" />,
      });
      toastShown.current = true;
    }
  }, [reason, signout]);

  return (
    <div className="flex items-center justify-center min-h-screen w-full p-6">
      <div className="w-full max-w-md">
        {/* Removed session expired message block, now handled by toast */}
        <AuthForm
          onAuthSuccess={() => {
            if (typeof window !== "undefined") {
              window.sessionStorage.setItem("showWelcomeToast", "1");
            }
            setShouldRedirect(true);
          }}
          onFadingOutDone={() => setIsFadingOut(true)}
        />
        {isFadingOut && (
          <div className="fixed inset-0 z-[9999] flex items-center justify-center">
            <AppBackground />
            <UnifiedLoader text="" />
          </div>
        )}
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginContent />
    </Suspense>
  );
}
