// Formatting utilities for dates, file names, and display names

/**
 * Format a date string or Date object to YYYY-MM-DD
 */
export function formatDateYMD(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toISOString().slice(0, 10);
}

/**
 * Format a date string or Date object to a locale date string
 */
export function formatDateLocale(date: string | Date | undefined | null): string {
  if (!date) return "";
  let d: Date;
  if (typeof date === 'string') {
    d = new Date(date);
    if (isNaN(d.getTime())) return "";
  } else if (date instanceof Date) {
    if (isNaN(date.getTime())) return "";
    d = date;
  } else {
    return "";
  }
  return d.toLocaleDateString();
}

/**
 * Format a style name from a slug (e.g., 'adventure-time' => 'Adventure Time')
 */
export function formatStyleName(style: string | undefined | null): string {
  if (!style || typeof style !== 'string') return '';
  return style.replace(/-/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase());
}

/**
 * Format a file name for download
 */
export function formatDownloadFileName(styleName: string, date: string | Date, shortId: string): string {
  return `${formatStyleName(styleName)} - ${formatDateYMD(date)} - ${shortId}.png`;
} 