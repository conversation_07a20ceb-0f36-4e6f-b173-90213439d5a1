
"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { ImageGrid } from "@/components/gallery/ImageGrid";
import { GeneratedImage } from "@/lib/types";
import { Palette } from "lucide-react";
import React from "react";

interface RecentCreationsProps {
  recentImages: GeneratedImage[];
  onShare: (imageId: string, isShared: boolean) => void;
  onDelete: (imageId: string) => void;
  justGeneratedImageId?: string | null;
  loading?: boolean;
  loadMore?: () => void;
  hasMore?: boolean;
}

export const RecentCreations = React.memo(function RecentCreations({
  recentImages,
  onShare,
  onDelete,
  justGeneratedImageId,
  loading = false,
  loadMore,
  hasMore,
}: RecentCreationsProps) {
  return (
    <div className="w-full">
      <Card className="glass-card card-entrance h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-xl">
            <div className="p-2 bg-gradient-to-r from-violet-500 to-blue-500 rounded-lg transition-all duration-300">
              <Palette className="h-5 w-5 text-white" />
            </div>
            <span>Recent Creations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ImageGrid
            images={recentImages}
            isLoading={loading}
            onShare={(image: GeneratedImage) => (onShare ? ((imageId: string, isShared: boolean) => onShare(imageId, isShared)) : undefined)}
            onDelete={onDelete}
            loadMore={loadMore}
            hasMore={hasMore}
            showDownloadButton={true}
            showSharedChip={true}
            hideLikeButton={true}
            columns={1}
            cardVariant="compact"
          />
        </CardContent>
      </Card>
    </div>
  );
});
