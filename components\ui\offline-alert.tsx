"use client";

import { useEffect, useRef, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { WifiOff, Wifi } from "lucide-react";

export function OfflineAlert() {
  const [isOnline, setIsOnline] = useState(typeof window !== 'undefined' ? navigator.onLine : true);
  const [showAlert, setShowAlert] = useState(false);
  const wasOffline = useRef(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (wasOffline.current) {
        setShowAlert(true);
        setTimeout(() => setShowAlert(false), 3000);
        wasOffline.current = false;
      }
    };
    const handleOffline = () => {
      setIsOnline(false);
      setShowAlert(true);
      wasOffline.current = true;
    };
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);
    // On mount, if offline, show the alert
    if (!navigator.onLine) {
      setIsOnline(false);
      setShowAlert(true);
      wasOffline.current = true;
    }
    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  if (!showAlert) return null;

  return (
    <div className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md px-4">
      <Alert
        className={`flex items-center gap-3 px-5 py-3 rounded-xl shadow-lg border-2 transition-colors duration-300
          ${isOnline
            ? "bg-green-600/90 border-green-400/80 text-green-50"
            : "bg-red-600/90 border-red-400/80 text-red-50"}
        `}
        style={{
          backdropFilter: "blur(6px)",
          boxShadow: "0 4px 24px 0 rgba(0,0,0,0.10)",
        }}
      >
        {isOnline ? <Wifi className="h-5 w-5 min-w-5" /> : <WifiOff className="h-5 w-5 min-w-5" />}
        <AlertDescription className="flex-1 text-base font-medium text-center">
          {isOnline
            ? "Connection restored! You're back online."
            : "You're offline. Some features may not work properly."}
        </AlertDescription>
      </Alert>
    </div>
  );
}
