
"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Download,
  Share2,
  Send,
  Trash2,
  Eye,
  Heart,
  Sparkles,
  RefreshCw,
  LogIn,
  Shield,
  Loader2,
} from "lucide-react";
import { GeneratedImage } from "@/lib/types";
import { useUser } from "@/lib/contexts/UserContext"; // To check if user is logged in
import dynamic from "next/dynamic";
import { cn } from "@/lib/utils";
import { ImageUrlHandler } from "@/lib/image-url-handler";
import { createBrowserClient } from '@supabase/ssr';
import React from "react";
// useSWR will be used by the imported hook
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipArrow,
} from "@/components/ui/tooltip";
import { useStyles } from "@/lib/contexts/StylesContext";
import { useImageLikeStatus } from "@/lib/hooks/useImageLikeStatus";
import useSWR from "swr";
import { showShareToast } from "@/lib/utils/toast";
import { formatStyleName, formatDateLocale } from "@/lib/utils/format";
import { useImageDownload } from "@/lib/hooks/useImageDownload";
import { useLikeToggle } from "@/lib/hooks/useLikeToggle";
import { useImageShare } from "@/lib/hooks/useImageShare";
import { useImageDelete } from "@/lib/hooks/useImageDelete";
import { useAnalytics } from "@/lib/hooks/useAnalytics";

interface UnifiedImageCardProps {
  image: GeneratedImage & { generating?: boolean };
  onShare?: (imageId: string, isShared: boolean) => void;
  onDelete?: (imageId: string) => void;
  showActions?: boolean;
  priority?: boolean;
  loading?: "eager" | "lazy";
  variant?: "default" | "compact" | "detailed";
  justGeneratedImageId?: string | null;
  showSharedChip?: boolean;
  showDownloadButton?: boolean;
  hideLikeButton?: boolean; // New prop to force-hide like button
}

const UnifiedImageModal = dynamic(
  () => import("./unified-image-modal").then((mod) => mod.UnifiedImageModal),
  { ssr: false, loading: () => null },
);

const DynamicDeleteConfirmationModal = dynamic(() => import("@/components/ui/delete-confirmation-modal").then(mod => mod.DeleteConfirmationModal), { ssr: false, loading: () => null });

// Intersection Observer hook
function useIntersectionObserver(
  ref: React.RefObject<Element | null>,
  options?: IntersectionObserverInit,
) {
  const [isInView, setIsInView] = useState(false);
  useEffect(() => {
    if (!ref.current) return;
    const observer = new window.IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setIsInView(true);
        observer.disconnect();
      }
    }, options);
    observer.observe(ref.current);
    return () => observer.disconnect();
  }, [ref, options]);
  return isInView;
}

// Helper to get the display name for a style
function getStyleDisplayNameById(styleId: string, styles: any[]): string {
  if (!styleId) return "";
  if (styles && Array.isArray(styles)) {
    const found = styles.find((s) => s.id === styleId);
    if (found) return found.name;
  }
  return (styleId || "")
    .replace(/-/g, " ")
    .replace(/\b\w/g, (c: string) => c.toUpperCase());
}

/**
 * UnifiedImageCard displays a generated image with actions.
 * The `priority` prop is used for LCP optimization and is passed to next/image.
 */
export function UnifiedImageCard({
  image,
  onShare,
  onDelete,
  showActions = true,
  priority = false,
  loading = "lazy",
  variant = "default",
  justGeneratedImageId,
  showSharedChip = false,
  showDownloadButton = true,
  hideLikeButton = false,
}: UnifiedImageCardProps) {
  const { user } = useUser();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [authError, setAuthError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [urlMethod, setUrlMethod] = useState<"direct" | "signed-url" | "proxy">("direct");
  const cardRef = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(cardRef, { rootMargin: "200px" });
  const [linkSharePulse, setLinkSharePulse] = useState(false);
  const [communitySharePulse, setCommunitySharePulse] = useState(false);
  const { styles } = useStyles();
  const { trackImageDownload, trackImageView } = useAnalytics();
  const [permanentError, setPermanentError] = useState<null | "forbidden" | "notfound">(null);
  const displayUrlRef = useRef<string | null>(null);
  const [displayUrlStable, setDisplayUrlStable] = useState<string | null>(null);
  const [optimisticIsShared, setOptimisticIsShared] = useState(image.is_shared);
  useEffect(() => {
    setOptimisticIsShared(image.is_shared);
  }, [image.is_shared]);
  const { likeCount, userLiked, isLoadingLikeStatus, mutateLikeStatus } = useImageLikeStatus(image.id, image.like_count);
  const isOwnImage = Boolean(user && user.id === image.user_id);
  const { handleLikeToggle, isLiking } = useLikeToggle({
    imageId: image.id,
    likeCount: likeCount ?? 0,
    userLiked,
    mutateLikeStatus,
    isOwnImage,
    user,
    isLoadingLikeStatus,
  });
  const { downloadImage, isDownloading } = useImageDownload();
  const { share: shareImage } = useImageShare();
  const { deleteImage, isDeleting } = useImageDelete();
  useEffect(() => {
    if (justGeneratedImageId && image.id === justGeneratedImageId) {
      setIsModalOpen(true);
    }
    if (justGeneratedImageId && image.id !== justGeneratedImageId) {
      setIsModalOpen(false);
    }
  }, [justGeneratedImageId, image.id]);

  const fetchCachedUrl = useCallback(async () => {
    try {
      const url = await ImageUrlHandler.getCachedImageUrl(
        image.id,
        image.image_url,
      );
      return url;
    } catch (err: any) {
      throw err;
    }
  }, [image.id, image.image_url]);

  const {
    data: swrDisplayUrl,
    error: swrError,
    isLoading: swrLoading,
  } = useSWR([image.id, image.image_url, "cached"], fetchCachedUrl, {
    revalidateOnFocus: false,
  });

  useEffect(() => {
    if (swrDisplayUrl && swrDisplayUrl !== displayUrlRef.current) {
      displayUrlRef.current = swrDisplayUrl;
      setDisplayUrlStable(swrDisplayUrl);
    }
    if (!swrDisplayUrl) {
      displayUrlRef.current = null;
      setDisplayUrlStable(null);
    }
  }, [swrDisplayUrl]);

  const isLoading = swrLoading;
  const imageError = !!swrError;

  useEffect(() => {
    if (swrError) {
      if ((swrError as any).code === 403) setPermanentError("forbidden");
      else if ((swrError as any).code === 404) setPermanentError("notfound");
      else setPermanentError(null);
    } else {
      setPermanentError(null);
    }
  }, [swrError]);

  const handleImageLoad = () => { setImageLoaded(true); };
  const handleImageError = () => {
    setAuthError(true);
    setImageLoaded(false);
  };
  const handleRetry = async () => { setRetryCount((prev) => prev + 1); };

  const handleDownload = async () => {
    if (!displayUrlStable) return;
    await downloadImage({
      displayUrl: displayUrlStable,
      styleName: styleName,
      createdAt: image.created_at,
      imageId: image.id,
      trackImageDownload,
    });
  };

  const handleDeleteConfirm = async () => {
    const result = await deleteImage(image.id, image);
    if (result.success) {
      setIsDeleteModalOpen(false);
      onDelete?.(image.id);
    }
  };

  const handleCardClick = (e: React.MouseEvent) => {
    if ((e.target as HTMLElement).closest(".action-buttons")) {
      return;
    }
    if (!imageError && !authError && displayUrlStable && imageLoaded) {
      trackImageView(image.id, image.style, "gallery");
      setIsModalOpen(true);
    } else if ((imageError || authError) && !permanentError) {
      handleRetry();
    }
  };

  const hasValidImage = displayUrlStable && !imageError && !authError;
  const styleName = image.generating ? '' : formatStyleName(image.style);
  const cardClasses = cn(
    "glass-card card-entrance overflow-hidden group transition-all duration-300 ease-out w-full min-w-0",
    isDeleting && "opacity-50 pointer-events-none",
    variant === "detailed" && "max-w-lg",
  );
  const aspectRatio = "aspect-square";
  const handleShare = async () => {
    setLinkSharePulse(true);
    setTimeout(() => setLinkSharePulse(false), 350);
    const imageUrl = `${window.location.origin}/images/${image.id}`;
    await shareImage(imageUrl, "Check out this AI artwork!");
  };
  const handleCommunityShare = (e: React.MouseEvent) => {
    setCommunitySharePulse(true);
    setTimeout(() => setCommunitySharePulse(false), 350);
    e.stopPropagation();
    setOptimisticIsShared((prev) => !prev);
    if (onShare) {
      onShare(image.id, !image.is_shared);
    }
  };

  if (image.generating) {
    return (
      <Card className={cn("glass-card card-entrance animate-pulse opacity-90 pointer-events-none select-none", "h-full")}>
        <CardContent className="p-0 flex flex-col items-center justify-center h-full rounded-2xl">
          <div className={cn("relative overflow-hidden w-full", aspectRatio, "flex flex-col items-center justify-center h-full rounded-2xl")}>
            <Loader2 className="h-12 w-12 text-violet-400 animate-spin mb-4" />
            <span className="text-lg font-semibold text-slate-300 text-center px-4">Generating your image...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading && !displayUrlStable) {
    return (
      <Card ref={cardRef} className={cardClasses + " animate-pulse"}>
        <CardContent className="p-0">
          <div className={cn("relative overflow-hidden rounded-2xl", aspectRatio, "bg-gradient-to-br from-slate-800 to-slate-900")}></div>
        </CardContent>
      </Card>
    );
  }

  if (permanentError === "forbidden" || permanentError === "notfound") {
    return (
      <Card ref={cardRef} className={cn(cardClasses, "bg-gradient-to-br from-slate-800 to-slate-900 opacity-60")}>
        <CardContent className="p-0 flex flex-col items-center justify-center h-full min-h-[180px]">
          <Shield className="h-10 w-10 text-yellow-400 mb-2" />
          <p className="text-sm text-slate-400 font-medium">{permanentError === "forbidden" ? "You do not have access to this image." : "Image not found."}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card ref={cardRef} className={cn(cardClasses, "cursor-pointer")} onClick={handleCardClick} onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)} tabIndex={0} role="button" aria-label="Open image modal">
        <CardContent className="p-0">
          <div className={cn("relative overflow-hidden w-full rounded-2xl", aspectRatio, isLoading || imageError || authError ? "bg-gradient-to-br from-slate-800 to-slate-900" : "bg-transparent")}>
            {isLoading && (<div className="absolute inset-0 w-full h-full bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 animate-pulse z-10" />)}
            {hasValidImage && (
              <div className="relative w-full h-full image-container">
                <Image src={displayUrlStable} alt={`Generated image in ${image.style} style`} fill sizes="100vw" className="object-contain w-full h-full rounded-2xl" onLoad={handleImageLoad} onError={handleImageError} loading={loading} priority={priority} placeholder="empty" style={{ zIndex: 1, imageRendering: "crisp-edges", backfaceVisibility: "hidden", transform: "translateZ(0)", willChange: "auto" }} />
                {!imageLoaded && (<div className="absolute inset-0 w-full h-full bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 animate-pulse transition-opacity duration-500 pointer-events-none opacity-100" style={{ zIndex: 2 }} />)}
              </div>
            )}
            <div className="absolute top-2 left-2 z-20">
              <div className="px-2 py-0.5 bg-slate-900/80 backdrop-blur-sm text-white text-[11px] font-medium rounded-md border border-slate-700/50 backface-hidden will-change-auto [transform:translateZ(0)] [image-rendering:crisp-edges]">
                <span className="bg-gradient-to-r from-violet-400 to-blue-400 bg-clip-text text-transparent font-semibold">{getStyleDisplayNameById(image.style, styles)}</span>
              </div>
            </div>
            <div className="absolute top-2 right-2 z-20 flex items-center gap-1.5">
              {showSharedChip && optimisticIsShared && (
                <div className={cn("px-1.5 py-0.5 bg-emerald-900/80 backdrop-blur-md text-emerald-200 text-[11px] font-medium rounded-md border border-emerald-700/50 flex items-center gap-1")} title="Shared">
                  <Share2 className="h-3 w-3" />
                </div>
              )}
              <div className={cn("px-1.5 py-0.5 bg-pink-700/80 backdrop-blur-md text-pink-200 text-[11px] font-medium rounded-md flex items-center gap-1 min-w-[32px]", likeCount > 0 ? "opacity-100" : "opacity-60")} title="Like count">
                <Heart className="h-3 w-3" fill={userLiked ? "currentColor" : "none"} />
                <span>{likeCount}</span>
              </div>
            </div>
            
            {showActions && isInView && hasValidImage && (
              <div className={cn("action-buttons absolute bottom-3 left-3 right-3 transition-all duration-300 ease-out z-20", isHovered ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2 pointer-events-none")}>
                <div className="flex items-center justify-center gap-2">
                  {user && !isOwnImage && !hideLikeButton && (
                    <TooltipProvider delayDuration={100}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" onClick={handleLikeToggle} disabled={isLiking || isLoadingLikeStatus} className={cn("h-8 w-8 backdrop-blur-md border transition-all duration-200 group/likebutton", userLiked ? "bg-pink-600/90 text-white border-pink-500/50 hover:bg-pink-500/90 hover:border-pink-400/50" : "bg-slate-900/90 text-slate-200 border-slate-700/50 hover:bg-slate-800/90 hover:border-slate-600/50 hover:text-white")} aria-label={userLiked ? "Unlike image" : "Like image"}>
                            <Heart className={cn("h-4 w-4 transition-all group-hover/likebutton:scale-110", userLiked ? "fill-current" : "fill-none")} fill={userLiked ? "currentColor" : "none"} />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent sideOffset={6}>{userLiked ? "Unlike" : "Like"}<TooltipArrow className="fill-slate-900/70" /></TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  {onShare && (
                    <TooltipProvider delayDuration={100}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" onClick={handleCommunityShare} className={cn("h-8 w-8 backdrop-blur-md border transition-all duration-200", optimisticIsShared ? "bg-emerald-900/90 text-emerald-200 border-emerald-700/50 hover:bg-emerald-800/90 hover:border-emerald-600/50" : "bg-slate-900/90 text-slate-200 border-slate-700/50 hover:bg-slate-800/90 hover:border-slate-600/50 hover:text-white", communitySharePulse && "animate-pulse")} aria-label={optimisticIsShared ? `Unshare from Community` : `Share in Community`} disabled={isLoading || !imageLoaded}>
                            <Share2 className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent sideOffset={6}>{optimisticIsShared ? "Unshare from Community" : "Share in Community"}<TooltipArrow className="fill-slate-900/70" /></TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" onClick={(e) => { e.stopPropagation(); showShareToast("copied"); handleShare(); }} className={cn("h-8 w-8 bg-slate-900/90 backdrop-blur-md text-blue-300 border border-blue-700/50 hover:bg-blue-800/90 hover:border-blue-600/50 hover:text-white transition-all duration-200", linkSharePulse && "animate-pulse")} aria-label={`Copy link to ${formatStyleName(image.style)} image`} disabled={isLoading || !imageLoaded}>
                          <Send className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent sideOffset={6}>Share to Socials<TooltipArrow className="fill-slate-900/70" /></TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  {showDownloadButton && (
                    <TooltipProvider delayDuration={100}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" onClick={(e) => { e.stopPropagation(); handleDownload(); }} className="h-8 w-8 bg-slate-900/90 backdrop-blur-md text-slate-200 border border-slate-700/50 hover:bg-slate-800/90 hover:border-slate-600/50 hover:text-white transition-all duration-200 text-xs" disabled={isDownloading || imageError || authError || isLoading || !imageLoaded} aria-label={`Download ${formatStyleName(image.style)} image`}>
                            {isDownloading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent sideOffset={6}>Download<TooltipArrow className="fill-slate-900/70" /></TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  {onDelete && (
                    <TooltipProvider delayDuration={100}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="icon" onClick={(e) => { e.stopPropagation(); setIsDeleteModalOpen(true); }} disabled={isDeleting} className="h-8 w-8 bg-slate-900/90 backdrop-blur-md text-red-400 border border-red-800/50 hover:bg-red-900/50 hover:border-red-700/50 hover:text-red-300 disabled:opacity-50 transition-all duration-200" aria-label={`Delete ${formatStyleName(image.style)} image`}>
                            {isDeleting ? (<Loader2 className="h-4 w-4 animate-spin" />) : (<Trash2 className="h-4 w-4" />)}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent sideOffset={6}>Delete<TooltipArrow className="fill-slate-900/70" /></TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      <DynamicDeleteConfirmationModal isOpen={isDeleteModalOpen} onClose={() => setIsDeleteModalOpen(false)} onConfirm={handleDeleteConfirm} isDeleting={isDeleting} itemName={`${formatStyleName(image.style)} artwork`} description={`This will permanently delete your ${formatStyleName(image.style)} artwork. The image file will be removed from secure storage and all metadata will be deleted from the database.`} />
      <UnifiedImageModal image={image} isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} onShare={onShare} onDelete={onDelete} showActions={showActions} />
    </>
  );
}

export const MemoizedUnifiedImageCard = React.memo(UnifiedImageCard);
