import { supabase } from "./supabase";
import { imageCache } from "./image-cache-service";

interface ImageUrlAnalysis {
  originalUrl: string;
  urlType:
    | "storage-path"
    | "legacy-supabase"
    | "dalle-blob"
    | "external"
    | "signed-url";
  needsProxy: boolean;
  needsSignedUrl: boolean;
  isValid: boolean;
  error?: string;
}

// IndexedDB helper for signed URL cache
const DB_NAME = "SignedUrlCacheDB";
const STORE_NAME = "signedUrls";
let dbPromise: Promise<IDBDatabase> | null = null;

function openDb(): Promise<IDBDatabase> {
  if (dbPromise) return dbPromise;
  dbPromise = new Promise((resolve, _reject) => {
    const request = indexedDB.open(DB_NAME, 1);
    request.onupgradeneeded = () => {
      request.result.createObjectStore(STORE_NAME);
    };
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => _reject(request.error);
  });
  return dbPromise;
}

async function getFromIndexedDb(
  key: string,
): Promise<{ url: string; expiresAt: number } | undefined> {
  try {
    const db = await openDb();
    return new Promise((resolve, _reject) => {
      const tx = db.transaction(STORE_NAME, "readonly");
      const store = tx.objectStore(STORE_NAME);
      const req = store.get(key);
      req.onsuccess = () => {
        if (req.result) {
          resolve(req.result);
        } else {
          resolve(undefined);
        }
      };
      req.onerror = () => resolve(undefined);
    });
  } catch {
    return undefined;
  }
}

async function setInIndexedDb(
  key: string,
  value: { url: string; expiresAt: number },
) {
  try {
    const db = await openDb();
    return new Promise((resolve, _reject) => {
      const tx = db.transaction(STORE_NAME, "readwrite");
      const store = tx.objectStore(STORE_NAME);
      const req = store.put(value, key);
      req.onsuccess = () => resolve(undefined);
      req.onerror = () => resolve(undefined);
    });
  } catch {
    // ignore
  }
}

// In-memory cache for signed URLs
const signedUrlCache: Record<string, { url: string; expiresAt: number }> = {};
// In-memory map for in-flight signed URL requests
const inflightSignedUrlRequests: Record<string, Promise<any>> = {};

export class ImageUrlHandler {
  static analyzeUrl(url: string): ImageUrlAnalysis {
    const analysis: ImageUrlAnalysis = {
      originalUrl: url,
      urlType: "external",
      needsProxy: false,
      needsSignedUrl: false,
      isValid: true,
    };

    try {
      // Check for new storage path format
      if (url.startsWith("private-images/")) {
        analysis.urlType = "storage-path";
        analysis.needsSignedUrl = true; // Private bucket needs signed URLs
        analysis.needsProxy = false; // Use signed URLs instead of proxy
        return analysis;
      }

      // Check for DALL-E blob URLs
      if (url.includes("blob.core.windows.net")) {
        analysis.urlType = "dalle-blob";
        analysis.needsProxy = true; // Still proxy DALL-E URLs for CORS
        analysis.needsSignedUrl = false;
        return analysis;
      }

      // Check for signed URLs (have token parameter)
      if (url.includes("token=")) {
        analysis.urlType = "signed-url";
        analysis.needsProxy = false;
        analysis.needsSignedUrl = false;
        return analysis;
      }

      // Check for legacy Supabase URLs
      if (url.includes("supabase") && url.includes("/storage/v1/object/")) {
        analysis.urlType = "legacy-supabase";
        // Check if it's a private bucket URL
        if (url.includes("/private-images/")) {
          analysis.needsSignedUrl = true;
          analysis.needsProxy = false;
        } else {
          analysis.needsProxy = false; // Public URLs don't need proxy
          analysis.needsSignedUrl = false;
        }
        return analysis;
      }

      // External URLs
      if (url.startsWith("http")) {
        analysis.urlType = "external";
        analysis.needsProxy = false;
        analysis.needsSignedUrl = false;
        return analysis;
      }

      // Invalid URL
      analysis.isValid = false;
      analysis.error = "Unrecognized URL format";
    } catch (error) {
      analysis.isValid = false;
      analysis.error =
        error instanceof Error ? error.message : "URL analysis failed";
    }

    return analysis;
  }

  static async generateDisplayUrl(
    imageId: string,
    imageUrl: string,
  ): Promise<{
    url: string | null;
    error?: string;
    requiresAuth: boolean;
    method: "direct" | "signed-url" | "proxy";
  }> {
    const analysis = this.analyzeUrl(imageUrl);
    if (!analysis.isValid) {
      return {
        url: null,
        error: analysis.error,
        requiresAuth: false,
        method: "direct",
      };
    }
    // If no special handling needed, return original URL
    if (!analysis.needsProxy && !analysis.needsSignedUrl) {
      return {
        url: imageUrl,
        error: undefined,
        requiresAuth: false,
        method: "direct",
      };
    }
    try {
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();
      if (sessionError || !session) {
        return {
          url: null,
          error: "Authentication required for private images",
          requiresAuth: true,
          method: analysis.needsSignedUrl ? "signed-url" : "proxy",
        };
      }
      if (analysis.needsSignedUrl) {
        // Extract file path from storage path
        let filePath: string;
        if (imageUrl.startsWith("private-images/")) {
          filePath = imageUrl.replace("private-images/", "");
        } else {
          const url = new URL(imageUrl);
          const pathParts = url.pathname.split("/");
          const bucketIndex = pathParts.findIndex(
            (part) => part === "private-images",
          );
          if (bucketIndex !== -1) {
            filePath = pathParts.slice(bucketIndex + 1).join("/");
          } else {
            throw new Error("Could not extract file path from URL");
          }
        }
        const cacheKey = filePath;
        const now = Date.now();
        // 1. Try IndexedDB cache first
        let cached: { url: string; expiresAt: number } | undefined = undefined;
        try {
          cached = await getFromIndexedDb(cacheKey);
        } catch {}
        if (cached && cached.expiresAt > now) {
          signedUrlCache[cacheKey] = cached;
          return {
            url: cached.url,
            error: undefined,
            requiresAuth: true,
            method: "signed-url",
          };
        }
        // 2. Fetch signed URL from API
        const response = await fetch("/api/get-signed-url", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ filePath, expiresIn: 3600 }),
        });
        const data = await response.json();
        if (!response.ok) {
          return {
            url: null,
            error: data.error,
            requiresAuth: true,
            method: "signed-url",
          };
        }
        if (data.signedUrl) {
          // Optionally cache the signed URL
          const expiresAt = now + 11.5 * 60 * 60 * 1000;
          signedUrlCache[cacheKey] = { url: data.signedUrl, expiresAt };
          setInIndexedDb(cacheKey, { url: data.signedUrl, expiresAt });
          return {
            url: data.signedUrl,
            error: undefined,
            requiresAuth: true,
            method: "signed-url",
          };
        }
        // 3. Deduplicate in-flight requests
        if (!inflightSignedUrlRequests[cacheKey]) {
          inflightSignedUrlRequests[cacheKey] = (async () => {
            const response = await fetch("/api/get-signed-url", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${session.access_token}`,
              },
              body: JSON.stringify({
                filePath: filePath,
                expiresIn: 43200, // 12 hours
              }),
            });
            if (!response.ok) {
              const errorData = await response.json();
              delete inflightSignedUrlRequests[cacheKey];
              // Surface 403/404 with a code for the frontend
              if (response.status === 403) {
                const err = new Error(errorData.error || "Forbidden");
                (err as any).code = 403;
                throw err;
              }
              if (response.status === 404) {
                const err = new Error(errorData.error || "Not found");
                (err as any).code = 404;
                throw err;
              }
              throw new Error(
                errorData.error || "Failed to generate signed URL",
              );
            }
            const { signedUrl } = await response.json();
            // Cache the signed URL with expiration (11.5 hours to be safe)
            const expiresAt = now + 11.5 * 60 * 60 * 1000;
            signedUrlCache[cacheKey] = { url: signedUrl, expiresAt };
            setInIndexedDb(cacheKey, { url: signedUrl, expiresAt });
            delete inflightSignedUrlRequests[cacheKey];
            return {
              url: signedUrl,
              error: undefined,
              requiresAuth: true,
              method: "signed-url",
            };
          })();
        }
        return await inflightSignedUrlRequests[cacheKey];
      }
      // Handle proxy for DALL-E URLs
      if (analysis.needsProxy) {
        const proxyUrl = `/api/image-proxy/${imageId}`;
        return {
          url: proxyUrl,
          error: undefined,
          requiresAuth: true,
          method: "proxy",
        };
      }
      // Fallback
      return {
        url: imageUrl,
        error: undefined,
        requiresAuth: false,
        method: "direct",
      };
    } catch (error) {
      return {
        url: null,
        error:
          error instanceof Error
            ? error.message
            : "Failed to generate display URL",
        requiresAuth: true,
        method: analysis.needsSignedUrl ? "signed-url" : "proxy",
      };
    }
  }

  /**
   * Enhanced getCachedImageUrl with intelligent caching
   */
  static async getCachedImageUrl(
    imageId: string,
    imageUrl: string,
  ): Promise<string | null> {
    try {
      // Check if we already have this image cached
      if (imageCache.isCached(imageUrl)) {
        return await imageCache.getImage(imageUrl);
      }

      // Generate the display URL
      const result = await this.generateDisplayUrl(imageId, imageUrl);

      if (!result.url) {
        return null;
      }

      // Get auth token if needed
      let authToken: string | undefined;
      if (result.requiresAuth) {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        authToken = session?.access_token;
      }

      // Use the cache service to get the image
      return await imageCache.getImage(result.url, authToken);
    } catch (error) {
      return null;
    }
  }

  /**
   * Preload images for better performance
   */
  static async preloadImages(
    images: Array<{ id: string; url: string }>,
  ): Promise<void> {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      // Generate display URLs for all images
      const urlPromises = images.map(async (image) => {
        try {
          const result = await this.generateDisplayUrl(image.id, image.url);
          return result.url;
        } catch (error) {
          return null;
        }
      });

      const displayUrls = (await Promise.allSettled(urlPromises))
        .map((result) => (result.status === "fulfilled" ? result.value : null))
        .filter((url) => url !== null) as string[];

      // Preload the images
      await imageCache.preloadImages(displayUrls, authToken);
    } catch (error) {
      console.error("❌ Error preloading images:", error);
    }
  }
}
