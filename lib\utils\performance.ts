// Performance monitoring utilities

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private observers: Set<(metric: PerformanceMetric) => void> = new Set();

  // Start timing a metric
  start(name: string, metadata?: Record<string, any>): void {
    this.metrics.set(name, {
      name,
      startTime: performance.now(),
      metadata,
    });
  }

  // End timing a metric
  end(
    name: string,
    additionalMetadata?: Record<string, any>,
  ): PerformanceMetric | null {
    const metric = this.metrics.get(name);
    if (!metric) {
      // Only warn for non-web-vital metrics
      const webVitals = ["cls", "lcp", "fid"];
      if (process.env.NODE_ENV === "development" && !webVitals.includes(name)) {
        console.warn(`Performance metric "${name}" not found`);
      }
      return null;
    }

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;

    if (additionalMetadata) {
      metric.metadata = { ...metric.metadata, ...additionalMetadata };
    }

    // Notify observers
    this.observers.forEach((observer) => observer(metric));

    // Log to console in development
    if (process.env.NODE_ENV === "development") {
      console.log(
        `⏱️ ${name}: ${metric.duration.toFixed(2)}ms`,
        metric.metadata,
      );
    }

    return metric;
  }

  // Measure a function execution
  async measure<T>(
    name: string,
    fn: () => Promise<T> | T,
    metadata?: Record<string, any>,
  ): Promise<T> {
    this.start(name, metadata);
    try {
      const result = await fn();
      this.end(name, { success: true });
      return result;
    } catch (error) {
      this.end(name, {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  // Get all metrics
  getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  // Clear metrics
  clear(): void {
    this.metrics.clear();
  }

  // Subscribe to metric updates
  subscribe(observer: (metric: PerformanceMetric) => void): () => void {
    this.observers.add(observer);
    return () => this.observers.delete(observer);
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for measuring component render time
export function usePerformanceMeasure(name: string, _dependencies: any[] = []) {
  React.useEffect(() => {
    performanceMonitor.start(`${name}-render`);

    return () => {
      performanceMonitor.end(`${name}-render`);
    };
  }, [name]);
}

// Utility for measuring API calls
export async function measureApiCall<T>(
  name: string,
  apiCall: () => Promise<T>,
  metadata?: Record<string, any>,
): Promise<T> {
  return performanceMonitor.measure(`api-${name}`, apiCall, metadata);
}

// Utility for measuring user interactions
export function measureUserInteraction(
  name: string,
  handler: (...args: any[]) => any,
  metadata?: Record<string, any>,
) {
  return async (...args: any[]) => {
    return performanceMonitor.measure(
      `interaction-${name}`,
      () => handler(...args),
      metadata,
    );
  };
}

// Web Vitals monitoring
export function monitorWebVitals() {
  if (typeof window === "undefined") return;

  // Monitor Largest Contentful Paint (LCP)
  if ("PerformanceObserver" in window) {
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      if (entries.length > 0) {
        const lastEntry = entries[entries.length - 1];
        if (lastEntry && typeof lastEntry.startTime === "number") {
          performanceMonitor.end("lcp", { value: lastEntry.startTime });
        }
      }
    });
    lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });
  }

  // Monitor First Input Delay (FID)
  if ("PerformanceObserver" in window) {
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        const fidEntry = entry as PerformanceEventTiming;
        if (
          typeof fidEntry.processingStart === "number" &&
          typeof fidEntry.startTime === "number"
        ) {
          performanceMonitor.end("fid", {
            value: fidEntry.processingStart - fidEntry.startTime,
          });
        }
      });
    });
    fidObserver.observe({ entryTypes: ["first-input"] });
  }

  // Monitor Cumulative Layout Shift (CLS)
  if ("PerformanceObserver" in window) {
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      let hadEntry = false;
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          hadEntry = true;
        }
      });
      if (hadEntry) {
        performanceMonitor.end("cls", { value: clsValue });
      }
    });
    clsObserver.observe({ entryTypes: ["layout-shift"] });
  }
}

// Export React for the hook
import React from "react";
