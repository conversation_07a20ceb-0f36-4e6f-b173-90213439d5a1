"use client";

import React from "react";

import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, Trash2, Refresh<PERSON>w } from "lucide-react";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isDeleting: boolean;
  itemName: string;
  description: string;
}

export function DeleteConfirmationModal({
  isOpen,
  onClose, // Make sure onClose resets ALL modal-related state, not just closes the modal
  onConfirm,
  isDeleting,
  itemName,
  description,
}: DeleteConfirmationModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md p-0 border-none bg-transparent shadow-none">
        <Card className="glass-card border border-red-500/30 bg-slate-900/90 backdrop-blur-2xl shadow-2xl animate-fadeInUp">
          <CardContent className="p-8 pb-4 flex flex-col items-center">
            <div className="flex flex-col items-center w-full">
              <div className="relative mb-4">
                <div className="absolute inset-0 bg-red-500/30 rounded-full blur-2xl animate-pulse" />
                <div className="relative p-5 bg-red-500/20 rounded-full border border-red-500/30 shadow-lg">
                  <AlertTriangle className="h-10 w-10 text-red-400 drop-shadow-lg animate-warning-pulse" />
                </div>
              </div>
              <DialogTitle className="text-2xl font-bold text-red-500 mb-2 text-center micro-bounce">
                Delete <span className="text-red-500">{itemName}</span>?
              </DialogTitle>
              <DialogDescription className="text-base text-slate-300 leading-relaxed text-center mb-2 fade-in">
                Are you sure? This will zap <span className="font-semibold text-red-400">your {itemName}</span> into the void—no take backs!
              </DialogDescription>
            </div>
            <div className="flex items-center gap-4 w-full mt-6">
              <Button
                variant="ghost"
                onClick={onClose}
                disabled={isDeleting}
                className="flex-1 border border-transparent text-slate-400 hover:bg-slate-800/60 hover:text-white transition-all duration-150"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={onConfirm}
                disabled={isDeleting}
                className={cn(
                  `flex-1 glass font-bold text-base flex items-center justify-center gap-2 py-2 px-4 rounded-xl border-0 text-white shadow-xl transition-all duration-300 btn-glow-red hover-lift focus:outline-none focus-visible:ring-4 focus-visible:ring-red-400/60 bg-slate-900/80
                  hover:bg-gradient-to-r hover:from-red-600 hover:to-red-400
                  focus:bg-gradient-to-r focus:from-red-600 focus:to-red-400
                  min-w-[200px]`,
                  isDeleting && "opacity-60 cursor-not-allowed"
                )}
                autoFocus
              >
                {isDeleting ? (
                  <>
                    <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                    Zapping...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-5 w-5 mr-2 text-white drop-shadow" />
                    Yes, delete it!
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
}

// Add the gradientShift keyframes if not already present
if (typeof window !== 'undefined') {
  const style = document.createElement('style');
  style.innerHTML = `@keyframes gradientShift { 0%,100%{background-position:0% 50%} 50%{background-position:100% 50%} }`;
  document.head.appendChild(style);
}
