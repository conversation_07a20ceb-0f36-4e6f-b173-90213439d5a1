import { Users } from "lucide-react";

export function CommunityHeader({ imagesCount }: { imagesCount: number }) {
  return (
    <div className="mb-2 page-transition">
      <div className="flex items-center gap-3">
        {/* Community Icon */}
        <div className="w-12 h-12 flex items-center justify-center">
          <Users className="h-8 w-8 text-emerald-400" />
        </div>
        
        {/* Title and Count */}
        <div className="flex flex-col">
          <h1 className="text-4xl font-bold gradient-text leading-tight">
            Community Gallery
          </h1>
          <p className="text-gray-400 text-sm">
            {imagesCount} shared artworks
          </p>
        </div>
      </div>
    </div>
  );
}
