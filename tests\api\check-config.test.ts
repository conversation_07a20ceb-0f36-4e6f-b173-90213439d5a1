// @ts-nocheck
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error: supertest types may not be present in dev env
import request from "supertest";
import { createServer, Server, IncomingMessage, ServerResponse } from "http";
// @ts-expect-error: Next.js route handler import is not typed for direct use
import handler from "../../../app/api/check-config/route";
import { checkConfigHandler } from "../../lib/api/checkConfigHandler";

describe("checkConfigHandler", () => {
  it("returns config status when env vars are set", () => {
    process.env.OPENAI_API_KEY = "sk-test";
    process.env.NEXT_PUBLIC_SUPABASE_URL = "url";
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = "anon";
    const result = checkConfigHandler();
    expect(result).toEqual({
      configured: true,
      supabaseConfigured: true,
    });
  });

  it("returns false if env vars are missing", () => {
    delete process.env.OPENAI_API_KEY;
    delete process.env.NEXT_PUBLIC_SUPABASE_URL;
    delete process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const result = checkConfigHandler();
    expect(result.configured).toBe(false);
    expect(result.supabaseConfigured).toBe(false);
  });
});
