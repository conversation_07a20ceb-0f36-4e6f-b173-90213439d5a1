"use client";
export const dynamic = "force-dynamic";
import { Suspense } from "react";
import { CommunityHeader } from "./components/CommunityHeader";
import { CommunityFilters } from "./components/CommunityFilters";
import { CommunityGalleryGrid } from "./components/CommunityGalleryGrid";
import { useCommunityLogic } from "./components/useCommunityLogic";
import { SortByType } from "./components/useCommunityLogic";
import ProtectedRoute from "@/components/auth/ProtectedRoute";

function CommunityContent() {
  const {
    images,
    filteredImages,
    isLoading,
    error,
    styleFilter,
    setStyleFilter,
    styles,
    sortOrder,
    setSortOrder,
    handleImageShare,
    isImageOwner,
    loadMore,
    hasMore,
  } = useCommunityLogic();

  return (
    <main className="flex flex-col gap-y-4 w-full min-w-0" aria-label="Community main content">
      <section>
        <CommunityHeader imagesCount={images.length} />
      </section>
      <section>
        <CommunityFilters
          styleFilter={styleFilter}
          setStyleFilter={setStyleFilter}
          styles={styles}
          sortOrder={sortOrder}
          setSortOrder={(s: string) => {
            const newOrder = s as SortByType;
            setSortOrder(newOrder);
          }}
        />
      </section>
      <section className="flex-1 flex flex-col">
        <CommunityGalleryGrid
          filteredImages={filteredImages}
          isLoading={isLoading}
          error={error}
          onShare={handleImageShare}
          isImageOwner={isImageOwner as (image: any) => boolean}
          loadMore={loadMore}
          hasMore={hasMore}
        />
      </section>
    </main>
  );
}

export default function CommunityPage() {
  return (
    <ProtectedRoute>
      <Suspense fallback={<div>Loading...</div>}>
        <CommunityContent />
      </Suspense>
    </ProtectedRoute>
  );
}
