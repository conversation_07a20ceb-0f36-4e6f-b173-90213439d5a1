export const dynamic = "force-dynamic";
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import { ReactNode } from 'react';
import UserLoaderWrapper from "@/components/UserLoaderWrapper";
import { UserProvider } from "@/lib/contexts/UserContext";

export default async function ProtectedLayout({ children }: { children: ReactNode }) {
  const cookieStore = await cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll: () => Array.from(cookieStore.getAll()).map((c: any) => ({ name: c.name, value: c.value })),
        setAll: (cookiesToSet) => {
          for (const { name, value, options } of cookiesToSet) {
            (cookieStore as any).set({ name, value, ...options });
          }
        }
      }
    }
  );
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error || !user) {
    redirect('/login?reason=session-expired');
  }
  return (
    <UserProvider initialUser={user}>
      <UserLoaderWrapper>
        {children}
      </UserLoaderWrapper>
    </UserProvider>
  );
}
