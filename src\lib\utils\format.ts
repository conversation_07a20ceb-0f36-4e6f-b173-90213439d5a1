
// Formatting utilities for dates, file names, and display names

/**
 * Format a date string or Date object to YYYY-MM-DD
 */
export function formatDateYMD(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toISOString().slice(0, 10);
}

/**
 * Format a date string or Date object to DD-MM-YY
 */
export function formatDateDMY(date: string | Date): string {
  const d = typeof date === "string" ? new Date(date) : date;
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
  const year = String(d.getFullYear()).slice(-2);
  return `${day}-${month}-${year}`;
}


/**
 * Format a date string or Date object to a locale date string
 */
export function formatDateLocale(date: string | Date | undefined | null): string {
  if (!date) return "";
  let d: Date;
  if (typeof date === 'string') {
    d = new Date(date);
    if (isNaN(d.getTime())) return "";
  } else if (date instanceof Date) {
    if (isNaN(date.getTime())) return "";
    d = date;
  } else {
    return "";
  }
  return d.toLocaleDateString();
}

/**
 * Format a style name from a slug (e.g., 'adventure-time' => 'Adventure Time')
 */
export function formatStyleName(style: string | undefined | null): string {
  if (!style || typeof style !== 'string') return '';
  return style.replace(/-/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase());
}

/**
 * Format a file name for download
 */
export function formatDownloadFileName(styleName: string, date: string | Date, shortId: string): string {
  return `PxlMorphAI - ${formatStyleName(styleName)} - ${formatDateDMY(date)} - ${shortId}.png`;
}

/**
 * Generates a shareable URL for Twitter (X).
 * @param url - The URL to share.
 * @param title - The text to include in the tweet.
 * @returns The Twitter share URL.
 */
export function getTwitterShareUrl(url: string, title: string): string {
  const shareUrl = new URL("https://twitter.com/intent/tweet");
  shareUrl.searchParams.set("url", url);
  shareUrl.searchParams.set("text", `${title} - Created with PxlMorph AI`);
  return shareUrl.toString();
}

/**
 * Generates a shareable URL for Facebook.
 * @param url - The URL to share.
 * @returns The Facebook share URL.
 */
export function getFacebookShareUrl(url: string): string {
  const shareUrl = new URL("https://www.facebook.com/sharer/sharer.php");
  shareUrl.searchParams.set("u", url);
  return shareUrl.toString();
}
