// DEPRECATED: Use @supabase/auth-helpers-nextjs for all new code. See https://supabase.com/docs/guides/auth/auth-helpers/nextjs#app-router
import { createBrowserClient } from '@supabase/ssr';
import { GeneratedImage, User } from "./types";
import { createClient } from "@supabase/supabase-js";

export const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export class SupabaseService {
  static async getCurrentUser(): Promise<User | null> {
    try {
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();
      if (error || !user) return null;

      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("*")
        .eq("id", user.id)
        .maybeSingle();

      if (userError) {
        return null;
      }

      if (!userData) {
        // Create user if doesn't exist
        const { data: newUser, error: insertError } = await supabase
          .from("users")
          .insert([{ id: user.id, email: user.email! }])
          .select()
          .single();

        if (insertError) throw insertError;
        return newUser as unknown as User; // Explicitly cast to unknown then User
      }

      return userData as unknown as User; // Also cast userData for consistency
    } catch (error) {
      return null;
    }
  }

  static async saveGeneratedImage(imageData: {
    image_url: string;
    style: string;
    user_id: string;
  }): Promise<GeneratedImage | null> {
    try {
      if (process.env.NODE_ENV === "development") {
        console.log("💾 Saving image metadata to database...");
      }

      // Save to database (storage upload is now handled server-side)
      const { data, error } = await supabase
        .from("images")
        .insert([imageData])
        .select()
        .single();

      if (error) throw error;

      if (process.env.NODE_ENV === "development") {
        console.log("✅ Image metadata saved successfully");
      }
      return data as unknown as GeneratedImage; // Cast to GeneratedImage
    } catch (error) {
      return null;
    }
  }

  /**
   * Enhanced getUserImages with comprehensive fetching and pagination
   * Ensures ALL user images are retrieved from the database
   */
  static async getUserImages(
    userId: string,
    limit: number = 20,
    offset: number = 0,
  ): Promise<GeneratedImage[]> {
    try {
      if (process.env.NODE_ENV === "development") {
        const DEBUG_FETCH_USER_IMAGES = process.env.DEBUG_FETCH_USER_IMAGES === "true";
        if (DEBUG_FETCH_USER_IMAGES) {
          console.log(
            `[SupabaseService] 🔍 Fetching user images for: ${userId}, limit: ${limit}, offset: ${offset}`,
          );
        }
      }
      const { data: images, error } = await supabase
        .from("images")
        .select("id, image_url, style, user_id, like_count, shared_at, created_at, is_shared")
        .eq("user_id", userId)
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1);
      if (error) {
        console.error("❌ Error fetching user images:", error);
        throw error;
      }
      if (process.env.NODE_ENV === "development") {
        console.log(`✅ Fetched ${images?.length || 0} user images.`);
      }
      return (images as unknown as GeneratedImage[]) || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Fetch ALL user images at once (no pagination)
   * For instant gallery loading
   */
  static async getAllUserImages(userId: string): Promise<GeneratedImage[]> {
    try {
      let allImages: GeneratedImage[] = [];
      const { data, error } = await supabase
        .from("images")
        .select("id, image_url, style, user_id, like_count, shared_at, created_at, is_shared")
        .eq("user_id", userId)
        .order("created_at", { ascending: false });

      if (error) {
        if (process.env.NODE_ENV === "development") {
          console.error("❌ Error fetching all user images:", error);
        }
        throw error;
      }
      
      allImages = (data as unknown as GeneratedImage[]) || [];
      return allImages;
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("Error fetching all user images:", error);
      }
      return [];
    }
  }

  /**
   * Enhanced getSharedImages with pagination to fetch shared images by page
   * Can sort by 'recent' (default) or 'trending'.
   * @param sortBy - 'recent' or 'trending'
   * @param limit - number of images per page
   * @param offset - offset for pagination
   */
  static async getSharedImages(
    sortBy: "recent" | "trending" = "recent",
    limit: number = 20,
    offset: number = 0,
  ): Promise<GeneratedImage[]> {
    try {
      let query;
      if (sortBy === "trending") {
        // Use the trending_images view for perfect trending order
        query = supabase
          .from("trending_images")
          .select("id, image_url, style, user_id, like_count, shared_at, created_at, is_shared")
      } else {
        query = supabase
          .from("images")
          .select("id, image_url, style, user_id, like_count, shared_at, created_at, is_shared")
          .eq("is_shared", true)
          .order("shared_at", { ascending: false, nullsFirst: false })
          .order("created_at", { ascending: false });
      }
      const { data: images, error } = await query.range(offset, offset + limit - 1);
      if (process.env.NODE_ENV === "development" && sortBy === "trending" && images) {
        console.log("[Trending Debug] Returned order:", images.map(img => ({ id: img.id, like_count: img.like_count })));
      }
      if (error) {
        if (process.env.NODE_ENV === "development") {
          console.error("❌ Error fetching shared images page:", error);
        }
        throw error;
      }
      if (process.env.NODE_ENV === "development") {
        console.log(`✅ Successfully fetched shared images page: ${images?.length || 0} (offset ${offset})`);
      }
      return (images as unknown as GeneratedImage[]) || [];
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("Error fetching shared images page:", error);
      }
      return [];
    }
  }

  /**
   * Enhanced storage listing function to get ALL files in bucket
   * Handles pagination and ensures complete file listing
   */
  static async getAllStorageFiles(userId: string): Promise<any[]> {
    try {
      if (process.env.NODE_ENV === "development") {
        console.log("🔍 Fetching ALL storage files for user:", userId);
      }

      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        console.error("❌ No session for storage listing");
        return [];
      }

      // Create service role client for comprehensive listing
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

      if (!supabaseUrl || !supabaseServiceKey) {
        console.error(
          "❌ Missing service role credentials for storage listing",
        );
        return [];
      }

      const serviceClient = createClient(supabaseUrl, supabaseServiceKey);

      let allFiles: any[] = [];
      let hasMore = true;
      let offset = 0;
      const batchSize = 100;

      // List files in user's folder with pagination
      while (hasMore) {
        if (process.env.NODE_ENV === "development") {
          console.log(
            `📦 Listing storage files: offset ${offset}, limit ${batchSize}`,
          );
        }

        const { data: files, error } = await serviceClient.storage
          .from("private-images")
          .list(userId, {
            limit: batchSize,
            offset: offset,
            sortBy: { column: "created_at", order: "desc" },
          });

        if (error) {
          console.error("❌ Error listing storage files:", error);
          break;
        }

        if (!files || files.length === 0) {
          hasMore = false;
          break;
        }

        allFiles = allFiles.concat(files);

        if (files.length < batchSize) {
          hasMore = false;
        } else {
          offset += batchSize;
        }

        if (process.env.NODE_ENV === "development") {
          console.log(
            `✅ Storage batch listed: ${files.length} files, total so far: ${allFiles.length}`,
          );
        }
      }

      if (process.env.NODE_ENV === "development") {
        console.log(
          `🎉 Successfully listed ALL storage files: ${allFiles.length} total`,
        );
      }
      return allFiles;
    } catch (error) {
      console.error("Error listing storage files:", error);
      return [];
    }
  }

  /**
   * Comprehensive sync function to ensure database and storage are in sync
   * This helps identify any missing images
   */
  static async syncUserImagesWithStorage(userId: string): Promise<{
    dbImages: GeneratedImage[];
    storageFiles: any[];
    missingInDb: any[];
    missingInStorage: GeneratedImage[];
  }> {
    try {
      if (process.env.NODE_ENV === "development") {
        console.log("🔄 Starting comprehensive sync for user:", userId);
      }

      // Get all images from database
      const dbImages = await this.getUserImages(userId);
      if (process.env.NODE_ENV === "development") {
        console.log(`📊 Database images: ${dbImages.length}`);
      }

      // Get all files from storage
      const storageFiles = await this.getAllStorageFiles(userId);
      if (process.env.NODE_ENV === "development") {
        console.log(`📊 Storage files: ${storageFiles.length}`);
      }

      // Find files in storage but not in database
      const missingInDb = storageFiles.filter((file) => {
        const filePath = `private-images/${userId}/${file.name}`;
        return !dbImages.some((img) => img.image_url === filePath);
      });

      // Find database records without corresponding storage files
      const missingInStorage = dbImages.filter((img) => {
        if (!img.image_url.startsWith("private-images/")) {
          return false; // Skip external URLs
        }

        const fileName = img.image_url.split("/").pop();
        return !storageFiles.some((file) => file.name === fileName);
      });

      if (process.env.NODE_ENV === "development") {
        console.log(`📊 Sync results:`, {
          dbImages: dbImages.length,
          storageFiles: storageFiles.length,
          missingInDb: missingInDb.length,
          missingInStorage: missingInStorage.length,
        });
      }

      return {
        dbImages,
        storageFiles,
        missingInDb,
        missingInStorage,
      };
    } catch (error) {
      console.error("Error syncing images with storage:", error);
      return {
        dbImages: [],
        storageFiles: [],
        missingInDb: [],
        missingInStorage: [],
      };
    }
  }

  static async toggleImageShare(
    imageId: string,
    isShared: boolean,
  ): Promise<boolean> {
    try {
      // Get current session/access token
      const {
        data: { session },
      } = await supabase.auth.getSession();

      const response = await fetch("/api/share-image", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(session?.access_token ? { Authorization: `Bearer ${session.access_token}` } : {}),
        },
        body: JSON.stringify({ imageId, isShared }),
      });

      if (!response.ok) {
        const data = await response.json().catch(() => ({}));
        throw new Error(data.error || "Failed to update share status");
      }

      // Revalidation is handled by the calling hook (useGalleryLogic)
      return true;
    } catch (error) {
      console.error("[SupabaseService] Error toggling image share:", error);
      return false;
    }
  }

  /**
   * Enhanced deleteImage with retry logic, robust error handling, and detailed result
   */
  static async deleteImage(imageId: string, maxRetries = 2): Promise<{ success: boolean; error?: string }> {
    if (!imageId) {
      const errorMsg = "Delete failed: No image ID provided.";
      console.error("❌ deleteImage called with missing imageId");
      alert(errorMsg);
      return { success: false, error: errorMsg };
    }
    if (process.env.NODE_ENV === "development") {
      console.log(`🗑️ Deleting image: ${imageId}`);
    }
    const DEBUG_DELETE_IMAGE = process.env.DEBUG_DELETE_IMAGE === "true";
    // Get current session
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      const errorMsg = "Delete failed: You must be signed in.";
      console.error("❌ No active session for deletion");
      alert(errorMsg);
      return { success: false, error: errorMsg };
    }
    const apiUrl = "/api/delete-image";
    const requestBody = { imageId };
    const requestHeaders = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${session.access_token}`,
    };
    if (DEBUG_DELETE_IMAGE) {
      console.log("➡️ Sending DELETE request to:", apiUrl);
      console.log("📦 Request body:", JSON.stringify(requestBody));
    }
    let lastError: any = null;
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch(apiUrl, {
          method: "DELETE",
          headers: requestHeaders,
          cache: "no-store",
          body: JSON.stringify(requestBody),
        });
        const responseText = await response.text();
        if (DEBUG_DELETE_IMAGE) {
          console.log("⬅️ Raw response status:", response.status);
          console.log("📥 Raw response body:", responseText);
        }
        let responseData;
        try {
          if (!responseText.trim()) {
            if (!response.ok) {
              throw new Error(
                `Delete API failed with status ${response.status} and empty response body`,
              );
            }
            if (DEBUG_DELETE_IMAGE) {
              console.warn("⚠️ Delete API returned an empty response body.");
            }
            if (response.ok && response.status !== 204) {
              throw new Error(
                "Empty response from delete API, but content was expected.",
              );
            }
            responseData = { success: response.ok };
          } else {
            responseData = JSON.parse(responseText);
          }
        } catch (jsonError) {
          lastError = jsonError;
          console.error(
            "❌ Failed to parse delete API response as JSON:",
            jsonError,
          );
          const parseErrorMessage =
            jsonError instanceof Error
              ? jsonError.message
              : "Unknown parsing error";
          throw new Error(
            `Delete API returned invalid JSON. Status: ${response.status}. Error: ${parseErrorMessage}. Response: ${responseText.substring(0, 100)}`,
          );
        }
        if (!response.ok) {
          lastError = responseData;
          console.error("❌ Delete API error response:", responseData);
          const errorMessage =
            responseData?.error ||
            `Delete API failed with status ${response.status}`;
          throw new Error(errorMessage);
        }
        if (!responseData.success) {
          lastError = responseData;
          console.error(
            "❌ Delete API returned unsuccessful result (success flag missing or false):",
            responseData,
          );
          throw new Error(
            responseData.error ||
              "Delete operation was not successful according to API response",
          );
        }
        if (process.env.NODE_ENV === "development") {
          console.log(`✅ Image deleted successfully: ${imageId}`);
        }
        // Only trigger revalidation after successful delete
        const { mutate } = await import("swr");
        mutate((key) => Array.isArray(key) && key[0] === "community-images", undefined, { revalidate: true });
        mutate(["community-images", "recent"], undefined, { revalidate: true });
        mutate(["community-images", "trending"], undefined, { revalidate: true });
        if (typeof window !== "undefined") {
          window.dispatchEvent(
            new CustomEvent("imageDeleted", {
              detail: { imageId },
            }),
          );
        }
        return { success: true };
      } catch (error: any) {
        lastError = error;
        if (DEBUG_DELETE_IMAGE) {
          console.warn(`Delete attempt ${attempt + 1} failed:`, error);
        }
        // Only retry on network errors or 5xx
        if (
          attempt < maxRetries &&
          (error instanceof TypeError ||
            (error.message && /network|fetch|timeout|5\d\d/.test(error.message.toLowerCase())))
        ) {
          // Exponential backoff: 200ms, 400ms, ...
          await new Promise((res) => setTimeout(res, 200 * Math.pow(2, attempt)));
          continue;
        } else {
          break;
        }
      }
    }
    const errorMsg = lastError?.message || lastError?.error || "Unknown error during image deletion.";
    console.error("❌ Error during image deletion:", lastError);
    return { success: false, error: errorMsg };
  }

  static async signUp(email: string, password: string) {
    return await supabase.auth.signUp({ email, password });
  }

  static async signIn(email: string, password: string) {
    return await supabase.auth.signInWithPassword({ email, password });
  }

  static async signOut() {
    return await supabase.auth.signOut();
  }

  // Helper method to check if an image URL is still valid
  static async validateImageUrl(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, { method: "HEAD" });
      return response.ok;
    } catch {
      return false;
    }
  }

  // Method to clean up expired image URLs
  static async cleanupExpiredImages(): Promise<void> {
    try {
      const { data: images, error } = await supabase
        .from("images")
        .select("id, image_url")
        .limit(100);

      if (error || !images) return;

      const expiredImages = [];

      for (const image of images) {
        const isValid = await this.validateImageUrl(image.image_url as string);
        if (!isValid) {
          expiredImages.push(image.id as string);
        }
      }

      if (expiredImages.length > 0) {
        if (process.env.NODE_ENV === "development") {
          console.log(`Found ${expiredImages.length} expired images`);
        }
        // Optionally delete or mark as expired
        // await supabase.from('images').delete().in('id', expiredImages);
      }
    } catch (error) {
      console.error("Error cleaning up expired images:", error);
    }
  }

  /**
   * Get all favorite style IDs for a user
   */
  static async getUserFavorites(userId: string): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from("user_favorites")
        .select("style_id")
        .eq("user_id", userId);
      if (error) throw error;
      return data
        ? data.map((row: { style_id: string | unknown }) =>
            String(row.style_id),
          )
        : [];
    } catch (error) {
      console.error("Error fetching user favorites:", error);
      return [];
    }
  }

  /**
   * Add a style to user favorites
   */
  static async addFavoriteStyle(
    userId: string,
    styleId: string,
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from("user_favorites")
        .insert([{ user_id: userId, style_id: styleId }]);
      if (error) throw error;
      return true;
    } catch (error) {
      console.error("Error adding favorite style:", error);
      return false;
    }
  }

  /**
   * Remove a style from user favorites
   */
  static async removeFavoriteStyle(
    userId: string,
    styleId: string,
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from("user_favorites")
        .delete()
        .eq("user_id", userId)
        .eq("style_id", styleId);
      if (error) throw error;
      return true;
    } catch (error) {
      console.error("Error removing favorite style:", error);
      return false;
    }
  }

  /**
   * Check if a style is a favorite for a user
   */
  static async isFavoriteStyle(
    userId: string,
    styleId: string,
  ): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from("user_favorites")
        .select("id")
        .eq("user_id", userId)
        .eq("style_id", styleId)
        .maybeSingle();
      if (error) throw error;
      return !!data;
    } catch (error) {
      console.error("Error checking favorite style:", error);
      return false;
    }
  }
}
